import paramiko
import pandas as pd
import numpy as np
import time
import sys
import os
from pathlib import Path
sys.path.append(os.path.dirname(Path.cwd()))
import Replenishment_Model_Functions_25 as rmf
import pyodbc
import re
import json
import csv
import zipfile



try:
    # Try to use __file__ to get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # Fallback to using the current working directory
    script_dir = os.getcwd()

# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(script_dir, os.pardir, 'config.json')

# Load the configuration from the JSON file
with open(config_path, 'r') as file:
    config = json.load(file)

# Extract the necessary details from the configuration
hostname = config['SSH_hostname']
username = config['SSH_username']
password = config['SSH_password']


ODBC_CONN = config['ODBC_connection']





@rmf.timeit
def ssh_table_create_tpnb(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name, tpnb_list):
    
    # Setup the connection
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(hostname=hostname, username="phrubos", password=password)
        print("\nConnection is done\n")
    except:
        print("\nNo connection!\n")  
    
    # Setup FTP client
    ftp_client = ssh_client.open_sftp()      
        
    ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table_tpnb.sql", wp_working_output / saved_name / f"{what_to_create}_create_table_tpnb.sql")
    
    file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table_tpnb.sql"
    
    start = start.strip("'")
    end = end.strip("'")
    
    # parameter the SQL file
    with open(file_path, 'r') as file:
        sql_content = file.read()

        start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
        end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
        
        modified_content = re.sub(start_pattern, f"'{start}'", sql_content)
        modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
        
        
        placeholder = "mstr.slad_tpnb IN ()"
        start_index = modified_content.find(placeholder)
        if start_index != -1:
            end_index = modified_content.find(')', start_index)
            if end_index != -1:
                existing_values = modified_content[start_index + len(placeholder):end_index].strip()
                modified_content = modified_content.replace(f"{placeholder}{existing_values})", f"{placeholder})")
        

        formatted_values = ', '.join(str(value) for value in tpnb_list)
        modified_content = modified_content.replace(placeholder, f"mstr.slad_tpnb IN ({formatted_values})")
        
        
        weeks_pattern = r"/\d+\sAS"

        modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
        


    with open(file_path, 'w') as file:
        file.write(modified_content)
        
        
    ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table_tpnb.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table_tpnb.sql")
    

    
    print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
        
        
    print(f"\nScript ({what_to_create}) is being started.....\n")
        
    stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_tpnb")
    
    exit_status = stdout.channel.recv_exit_status()
    
    flag = 0
    if exit_status == 0:
        print(f"\nScript ({what_to_create}) finished successfully.\n")
    else:
        print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
        flag+=1
        
    ssh_client.close()
    
    return flag


@rmf.timeit
def ssh_downloader_tpnb(what_to_download: str, wp_working_output: str, saved_name: str, tpnb_dict: dict):
    

    folder_name = what_to_download
        
    # =============================================================================
    # Paths
    # =============================================================================
    
    output_folder = f"/home/<USER>/{folder_name}/output/"
    main_folder = f"/home/<USER>/{folder_name}/"
    
    # wp_working_output = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\inputs\files_for_dataset\SSH"
    try:
        ssh_client = paramiko.SSHClient()
        ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_client.connect(hostname=hostname, username='phrubos', password=password)
        print("\nConnection is done\n")
    except:
        print("\nNo connection!\n")
        
    
        
    ftp_client = ssh_client.open_sftp()
    
    if what_to_download == 'losses':
        ftp_client.put(wp_working_output / saved_name / "select.sql", main_folder + "select.sql")
        
    
    def download_part(countries):    
        # =============================================================================
        # Download files
        # =============================================================================

        for country in countries:
            
            print(f"Start to process of {what_to_download} ({country})")
            
            ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}.csv")
            time.sleep(5)
            csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
            
            if what_to_download in ['losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
                csv_id = csv_id-1
            
            print(f"\nThere will be {csv_id} rounds in {country}!\n")
            
            del_files = f'rm -f {main_folder}output/*'
            stdin, stdout, stderr = ssh_client.exec_command(del_files)
            
            stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")
            
    
            
            # print(stdout.readlines())
            time.sleep(5)
            
            counter = 0
            while counter <= csv_id:
            
        
                file_list = ftp_client.listdir(output_folder)
                
                try:
                    output = [i for i in file_list if  i.__contains__(".zip")][0]
                except:
                    output = []
                    pass
                
                if output.__contains__("zip"):
                    
                    print(f"\n{output} done!\n")
                    counter += 1
                    ftp_client.get(output_folder + output, wp_working_output / saved_name / output)
                    print("Copied onto local computer\n")
                    time.sleep(15)
                    ftp_client.remove(output_folder + output)
                    
        
                    
                else:
                    print("Downloading.....")
                    time.sleep(10)
                    
                if counter == csv_id:
                    
                    break
                    time.sleep(15)
            
        ftp_client.close()
        ssh_client.close()
    
    for k, v in tpnb_dict.items():
        download_part([k])
    
    print("\nData are downloaded! Check if it is needed to re-download again...")
           

    
    check_to_redownload = []
    found_elements = set()
    
    while True:
        check_to_redownload.clear()
        found_elements.clear()
    
        for root, dirs, files in os.walk(wp_working_output / saved_name):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.zip') and os.path.getsize(file_path) < 400 and "dotcom" not in file:  # Check for 1KB size
                    check_to_redownload.append(file)

        for element in check_to_redownload:
            for sub_element in ['HU', 'SK', 'CZ']:
                if sub_element in element:
                    found_elements.add(sub_element)
    
        if found_elements:
            print("\nThis country needs to download again!:", found_elements)
            print(f"The reason is: {check_to_redownload}\n")
            download_part(list(found_elements))
        else:
            print("\nNo need to re-download any files!!")
            break
    

    
    csv_files = [f for f in os.listdir(wp_working_output/ saved_name) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    if what_to_download == "losses":
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',', skiprows=6, skipfooter=1) for f in csv_files])        

        
    if not what_to_download == "losses":
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') for f in csv_files])
        
    # saving the file
    ce_df.to_parquet(wp_working_output / saved_name / f"{what_to_download}_{saved_name}", compression="gzip")
        
    # removing the zip files
    files = [f for f in os.listdir(
        wp_working_output / saved_name
        ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    for c in files:
        os.remove(
            os.path.join(
                wp_working_output / saved_name, f"{c}"
            )
        )
    return ce_df


@rmf.timeit
def ssh_table_create(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name):
    
    if what_to_create == "srd":
        
        success = run_srd_script(hostname, password)
        
        if success:
            flag = 0
            print("=" * 50)
            print("🎉 All done! Your SRD table is ready.")
        else:
            print("💥 Something went wrong. Check the server logs.")
            print("=" * 50)
        
    else:
    
        # Setup the connection
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username="phrubos", password=password)
            print("\nConnection is done\n")
        except:
            print("\nNo connection!\n")  
        
        # Setup FTP client
        ftp_client = ssh_client.open_sftp()      
            
        ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql", wp_working_output / saved_name / f"{what_to_create}_create_table.sql")
        
        file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table.sql"
        
        start = start.strip("'")
        end = end.strip("'")
        
        # parameter the SQL file
        with open(file_path, 'r') as file:
            sql_content = file.read()
    
            start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
            end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
            
            modified_content = re.sub(start_pattern, f"'{start}'", sql_content)
            modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
            
            
            # weeks_pattern = r"/\d+\sAS"
    
            # modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
            
            if pmg != None:
            
                # New pattern to replace PMG values
                pmg_pattern = r"WHERE SUBSTRING\(pmg, 1, 3\) IN \([^)]+\)"
                modified_content = re.sub(pmg_pattern, f"WHERE SUBSTRING(pmg, 1, 3) IN {pmg}", modified_content)
            
    
    
        with open(file_path, 'w') as file:
            file.write(modified_content)
            
            
        ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql")
        
    
        
        print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
            
            
        print(f"\nScript ({what_to_create}) is being started.....\n")
            
        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_q")
        
        exit_status = stdout.channel.recv_exit_status()
        
        flag = 0
        if exit_status == 0:
            print(f"\nScript ({what_to_create}) finished successfully.\n")
        else:
            print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
            flag+=1
            
        ssh_client.close()
    
    return flag



@rmf.timeit
def ssh_downloader_(what_to_download: str, wp_working_output: str, saved_name: str, stores: tuple):
    
    only_store_s = False
    
    
    if len(stores) < 10:
        only_store_s = True
        
    

    folder_name = what_to_download
        
    # =============================================================================
    # Paths
    # =============================================================================
    
    output_folder = f"/home/<USER>/{folder_name}/output/"
    main_folder = f"/home/<USER>/{folder_name}/"
    
    
    
    def download_part(countries: list, only_store_s: bool, stores ):    
        # =============================================================================
        # Download files
        # =============================================================================
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username='phrubos', password=password)
            print("\nConnection is done\n")
        except:
            print("\nNo connection!\n")
            
        
            
        ftp_client = ssh_client.open_sftp()
        
        if what_to_download == 'losses':
            ftp_client.put(wp_working_output /  "losses_download.sql", main_folder + "losses_download.sql")
    
            

        for country in countries:
            
            print(f"Start to process of {what_to_download} ({country})")
            
            
            if not only_store_s:
                
            
                ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}.csv")
                time.sleep(5)
    
                csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                
                if what_to_download in ['srd','losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
                    csv_id = csv_id-1
                    
                print(f"\nThere will be {csv_id} rounds in {country}!\n")
                
                del_files = f'rm -f {main_folder}output/*'
                stdin, stdout, stderr = ssh_client.exec_command(del_files)
                
                stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")    
                    
            if only_store_s:  
                
                
                selected_stores_set = set(map(str, stores))
                
                ftp_client.get(f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv")
                
                file_path = wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv"
                
                
                # Categorize filtered stores based on the starting digit
                cz_stores = sorted(store for store in selected_stores_set if store.startswith('1') and country == "CZ")
                sk_stores = sorted(store for store in selected_stores_set if store.startswith('2') and country == "SK")
                hu_stores = sorted(store for store in selected_stores_set if store.startswith('4') and country == "HU")
                
                # Write the filtered stores to the CSV file
                with open(file_path, mode='w', newline='') as file:
                    writer = csv.writer(file, delimiter='|')
                    if cz_stores:
                        writer.writerow(['CZ', '1', ','.join(cz_stores)])
                    if sk_stores:
                        writer.writerow(['SK', '1', ','.join(sk_stores)])
                    if hu_stores:
                        writer.writerow(['HU', '1', ','.join(hu_stores)])
                
                ftp_client.put(wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv", f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv")
                
                
                
                
                ftp_client.get(main_folder + f"parameters_all_groups_{country}_store_spec.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv")
                time.sleep(5)
    
                csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                
                    
                print(f"\nThere will be {csv_id} rounds in {country}!\n")
                
                del_files = f'rm -f {main_folder}output/*'
                stdin, stdout, stderr = ssh_client.exec_command(del_files)
                
                stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}_store_spec")
                
                
                    
            

            
    
            
            # print(stdout.readlines())
            time.sleep(5)
            
            counter = 0
            while counter <= csv_id:
            
        
                file_list = ftp_client.listdir(output_folder)
                
                try:
                    output = [i for i in file_list if  i.__contains__(".zip")][0]
                except:
                    output = []
                    pass
                
                if output.__contains__("zip"):
                    
                    print(f"\n{output} done!\n")
                    counter += 1
                    ftp_client.get(output_folder + output, wp_working_output / saved_name / output)
                    print("Copied onto local computer\n")
                    if only_store_s:
                        time.sleep(5)
                    if not only_store_s:   
                        time.sleep(15)
                    ftp_client.remove(output_folder + output)
                    
        
                    
                else:
                    print("Downloading.....")
                    
                    if only_store_s:
                        time.sleep(2)
                    if not only_store_s:
                        time.sleep(10)
                    
                if counter == csv_id:
                    
                    break
                    if only_store_s:
                        time.sleep(2)
                    if not only_store_s:
                        time.sleep(15)

            
        ftp_client.close()
        ssh_client.close()
        
    def map_tuple_to_countries(input_tuple):
        country_codes = {
            '1': 'CZ',
            '2': 'SK',
            '4': 'HU'
        }
        
        result = []
        for number in input_tuple:
            starting_digit = str(number)[0]
            if starting_digit in country_codes:
                country = country_codes[starting_digit]
                if country not in result:
                    result.append(country)
        
        return result
    
    
    download_part(map_tuple_to_countries(stores), only_store_s, stores)
    
    print("\nData are downloaded! Check if it is needed to re-download again...")
    
        
    check_to_redownload = []
    found_elements = set()
    
    while True:
        check_to_redownload.clear()
        found_elements.clear()
    
        for root, dirs, files in os.walk(wp_working_output / saved_name):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.zip') and os.path.getsize(file_path) < 400 and "dotcom" not in file:  # Check for 1KB size
                    check_to_redownload.append(file)

        for element in check_to_redownload:
            for sub_element in ['HU', 'SK', 'CZ']:
                if sub_element in element:
                    found_elements.add(sub_element)
    
        if found_elements:
            print("\nThis country needs to download again!:", found_elements)
            print(f"The reason is: {check_to_redownload}\n")
            download_part(list(found_elements), only_store_s, stores)
        else:
            print("\nNo need to re-download any files!!")
            break
    


        
        
        
        
    csv_files = [f for f in os.listdir(wp_working_output/ saved_name) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    if what_to_download == "losses":
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',', skiprows=6, skipfooter=1) for f in csv_files])        
        # saving the file
        ce_df.to_parquet(wp_working_output / saved_name / f"{what_to_download}_{saved_name}_raw_data", compression="gzip")

        
    if not what_to_download in ("losses", "srd"):
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') for f in csv_files])
        
        
        
        
        
        
        
    if what_to_download == "srd":
        
        ce_df = pd.concat([pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',') for f in csv_files])
        
        
        

import os

from tqdm import tqdm
from datetime import datetime
import colorama
from colorama import Fore, Back, Style



@rmf.timeit
def ssh_downloader(what_to_download: str, wp_working_output: str, saved_name: str, stores: tuple):
# Initialize colorama for colored output
    colorama.init()
    
    def print_banner():
        """Print a fancy banner"""
        print(f"""
    {Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
    ║                    🚀 SSH DOWNLOADER 2.0 🚀                   ║
    ║              Enhanced with Progress & Style                  ║
    ╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
    """)
    
    def print_status(message, status_type="info"):
        """Print colored status messages"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if status_type == "success":
            print(f"{Fore.GREEN}✅ [{timestamp}] {message}{Style.RESET_ALL}")
        elif status_type == "error":
            print(f"{Fore.RED}❌ [{timestamp}] {message}{Style.RESET_ALL}")
        elif status_type == "warning":
            print(f"{Fore.YELLOW}⚠️  [{timestamp}] {message}{Style.RESET_ALL}")
        elif status_type == "info":
            print(f"{Fore.CYAN}ℹ️  [{timestamp}] {message}{Style.RESET_ALL}")
        elif status_type == "processing":
            print(f"{Fore.MAGENTA}⚙️  [{timestamp}] {message}{Style.RESET_ALL}")


    
    print_banner()
    start_time = time.time()
    
    only_store_s = False
    
    if len(stores) < 10:
        only_store_s = True
        print_status(f"Mode: Store-specific ({len(stores)} stores)", "info")
    else:
        print_status(f"Mode: All groups ({len(stores)} stores)", "info")
        
    folder_name = what_to_download
        
    # =============================================================================
    # Paths
    # =============================================================================
    
    output_folder = f"/home/<USER>/{folder_name}/output/"
    main_folder = f"/home/<USER>/{folder_name}/"
    
    print_status(f"Target: {what_to_download}", "info")
    print_status(f"Output folder: {output_folder}", "info")
    
    def download_part(countries: list, only_store_s: bool, stores):    
        # =============================================================================
        # Download files
        # =============================================================================
        print_status("Establishing SSH connection...", "processing")
        
        try:
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            ssh_client.connect(hostname=hostname, username='phrubos', password=password)
            print_status("SSH Connection established successfully!", "success")
        except Exception as e:
            print_status(f"SSH Connection failed: {str(e)}", "error")
            return
            
        ftp_client = ssh_client.open_sftp()
        
        if what_to_download == 'losses':
            print_status("Uploading losses_download.sql...", "processing")
            ftp_client.put(wp_working_output / "losses_download.sql", main_folder + "losses_download.sql")
            print_status("SQL file uploaded successfully!", "success")

        for country in countries:
            
            print(f"\n{Fore.YELLOW}{'='*60}")
            print(f"🌍 PROCESSING COUNTRY: {country}")
            print(f"{'='*60}{Style.RESET_ALL}")
            
            if not only_store_s:
                print_status(f"Downloading parameters for {country} (all groups)", "processing")
                
                ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}.csv")
                time.sleep(2)
    
                csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                
                if what_to_download in ['srd','losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
                    csv_id = csv_id-1
                    
                print_status(f"Found {csv_id} processing rounds for {country}!", "success")
                
                print_status("Cleaning remote output directory...", "processing")
                del_files = f'rm -f {main_folder}output/*'
                stdin, stdout, stderr = ssh_client.exec_command(del_files)
                
                print_status(f"Starting remote processing for {country}...", "processing")
                stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")    
                    
            if only_store_s:  
                print_status(f"Processing specific stores for {country}", "processing")
                
                selected_stores_set = set(map(str, stores))
                
                ftp_client.get(f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv")
                
                file_path = wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv"
                
                # Categorize filtered stores based on the starting digit
                cz_stores = sorted(store for store in selected_stores_set if store.startswith('1') and country == "CZ")
                sk_stores = sorted(store for store in selected_stores_set if store.startswith('2') and country == "SK")
                hu_stores = sorted(store for store in selected_stores_set if store.startswith('4') and country == "HU")
                
                print_status(f"Filtered stores - CZ: {len(cz_stores)}, SK: {len(sk_stores)}, HU: {len(hu_stores)}", "info")
                
                # Write the filtered stores to the CSV file
                with open(file_path, mode='w', newline='') as file:
                    writer = csv.writer(file, delimiter='|')
                    if cz_stores:
                        writer.writerow(['CZ', '1', ','.join(cz_stores)])
                    if sk_stores:
                        writer.writerow(['SK', '1', ','.join(sk_stores)])
                    if hu_stores:
                        writer.writerow(['HU', '1', ','.join(hu_stores)])
                
                ftp_client.put(wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv", f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv")
                
                ftp_client.get(main_folder + f"parameters_all_groups_{country}_store_spec.csv", wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv")
                time.sleep(2)
    
                csv_id = pd.read_csv(wp_working_output / saved_name / f"parameters_all_groups_{country}_store_spec.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                    
                print_status(f"Found {csv_id} processing rounds for {country}!", "success")
                
                print_status("Cleaning remote output directory...", "processing")
                del_files = f'rm -f {main_folder}output/*'
                stdin, stdout, stderr = ssh_client.exec_command(del_files)
                
                print_status(f"Starting remote processing for {country} (store-specific)...", "processing")
                stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}_store_spec")

            print_status("Waiting for remote processing to initialize...", "processing")
            time.sleep(5)
            
            # Enhanced download loop with progress bar
            print(f"\n{Fore.GREEN}📥 DOWNLOADING FILES FOR {country}{Style.RESET_ALL}")
            
            counter = 0
            downloaded_files = []
            
            # Create progress bar
            pbar = tqdm(total=csv_id, desc=f"🔄 {country} Progress", 
                       bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]",
                       colour='green')
            
            while counter <= csv_id:
                try:
                    file_list = ftp_client.listdir(output_folder)
                    
                    try:
                        output = [i for i in file_list if i.__contains__(".zip")][0]
                    except:
                        output = []
                        pass
                    
                    if output and output.__contains__("zip"):
                        
                        # Download the file
                        local_file_path = wp_working_output / saved_name / output
                        ftp_client.get(output_folder + output, local_file_path)
                        
                        # Get file size for display
                        file_size = os.path.getsize(local_file_path)
                        size_mb = file_size / (1024 * 1024)
                        
                        downloaded_files.append(output)
                        counter += 1
                        
                        # Update progress bar
                        pbar.update(1)
                        pbar.set_postfix({"Last file": output[:30] + "..." if len(output) > 30 else output, 
                                         "Size": f"{size_mb:.1f}MB"})
                        
                        print_status(f"Downloaded: {output} ({size_mb:.1f}MB)", "success")
                        
                        # Clean up remote file
                        if only_store_s:
                            time.sleep(2)
                        else:   
                            time.sleep(5)
                        ftp_client.remove(output_folder + output)
                        
                    else:
                        # Show waiting status
                        pbar.set_postfix({"Status": "Waiting for files...", "Files ready": len(file_list)})
                        
                        if only_store_s:
                            time.sleep(2)
                        else:
                            time.sleep(10)
                    
                    if counter == csv_id:
                        break
                        
                except Exception as e:
                    print_status(f"Error during download: {str(e)}", "error")
                    if only_store_s:
                        time.sleep(2)
                    else:
                        time.sleep(5)
            
            pbar.close()
            print_status(f"Completed downloading {len(downloaded_files)} files for {country}!", "success")
            
        ftp_client.close()
        ssh_client.close()
        print_status("SSH connection closed", "info")
        
    def map_tuple_to_countries(input_tuple):
        country_codes = {
            '1': 'CZ',
            '2': 'SK',
            '4': 'HU'
        }
        
        result = []
        for number in input_tuple:
            starting_digit = str(number)[0]
            if starting_digit in country_codes:
                country = country_codes[starting_digit]
                if country not in result:
                    result.append(country)
        
        return result
    
    countries = map_tuple_to_countries(stores)
    print_status(f"Countries to process: {', '.join(countries)}", "info")
    
    # Main download process
    download_part(countries, only_store_s, stores)
    
    print(f"\n{Fore.CYAN}🔍 CHECKING FOR FAILED DOWNLOADS...{Style.RESET_ALL}")
    
    check_to_redownload = []
    found_elements = set()
    retry_count = 0
    max_retries = 3
    
    while retry_count < max_retries:
        check_to_redownload.clear()
        found_elements.clear()
    
        print_status("Scanning downloaded files for issues...", "processing")
        
        for root, dirs, files in os.walk(wp_working_output / saved_name):
            for file in files:
                file_path = os.path.join(root, file)
                if file.endswith('.zip') and os.path.getsize(file_path) < 400 and "dotcom" not in file:
                    check_to_redownload.append(file)
                    file_size = os.path.getsize(file_path)
                    print_status(f"Found problematic file: {file} ({file_size} bytes)", "warning")

        for element in check_to_redownload:
            for sub_element in ['HU', 'SK', 'CZ']:
                if sub_element in element:
                    found_elements.add(sub_element)
    
        if found_elements:
            retry_count += 1
            print_status(f"Retry {retry_count}/{max_retries} - Countries to re-download: {', '.join(found_elements)}", "warning")
            print_status(f"Problematic files: {', '.join(check_to_redownload)}", "warning")
            download_part(list(found_elements), only_store_s, stores)
        else:
            print_status("All files downloaded successfully! ✨", "success")
            break
    
    if retry_count >= max_retries:
        print_status(f"Maximum retries ({max_retries}) reached. Some files may still have issues.", "warning")

    # File processing section with progress
    print(f"\n{Fore.MAGENTA}📊 PROCESSING DOWNLOADED FILES...{Style.RESET_ALL}")
    
    csv_files = [f for f in os.listdir(wp_working_output / saved_name) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    print_status(f"Found {len(csv_files)} files to process", "info")
    
    if csv_files:
        print_status("Combining files into dataset...", "processing")
        
        # Show progress while reading files
        file_progress = tqdm(csv_files, desc="📂 Reading files", colour='blue')
        
        if what_to_download == "losses":
            dataframes = []
            for f in file_progress:
                file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                df = pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',', skiprows=6, skipfooter=1, engine='python')
                dataframes.append(df)
            
            ce_df = pd.concat(dataframes, ignore_index=True)
            
            # Save the file
            output_file = wp_working_output / saved_name / f"{what_to_download}_{saved_name}_raw_data.parquet"
            ce_df.to_parquet(output_file, compression="gzip")
            print_status(f"Saved as parquet: {output_file}", "success")

        elif what_to_download == "srd":
            dataframes = []
            for f in file_progress:
                file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                df = pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',')
                dataframes.append(df)
            
            ce_df = pd.concat(dataframes, ignore_index=True)
            
        elif not what_to_download in ("losses", "srd"):
            dataframes = []
            for f in file_progress:
                file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                df = pd.read_csv(os.path.join(wp_working_output / saved_name, f), sep=',')
                dataframes.append(df)
            
            ce_df = pd.concat(dataframes, ignore_index=True)
        
        file_progress.close()
        
        print_status(f"Final dataset shape: {ce_df.shape[0]:,} rows × {ce_df.shape[1]} columns", "success")
        
        
        
    # saving the file
    ce_df.to_parquet(wp_working_output / saved_name / f"{what_to_download}_{saved_name}", compression="gzip")
        
    # # removing the zip files
    # files = [f for f in os.listdir(
    #     wp_working_output / saved_name
    #     ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
    
    # for c in files:
    #     os.remove(
    #         os.path.join(
    #             wp_working_output / saved_name, f"{c}"
    #         )
    #     )
    
    
    # Final summary
    total_time = time.time() - start_time
    print(f"""
                {Fore.GREEN}╔══════════════════════════════════════════════════════════════╗
                ║                        🎉 DOWNLOAD COMPLETE! 🎉               ║
                ║                                                              ║
                ║  Total Time: {total_time/60:.1f} minutes                                ║
                ║  Files Processed: {len(csv_files) if csv_files else 0}                                      ║
                ║  Countries: {', '.join(countries)}                                  ║
                ║  Mode: {'Store-specific' if only_store_s else 'All groups'}                                    ║
                ╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
                """)        

    return ce_df

@rmf.timeit
def hierarchy_to_sold_item(item_sold_dir):
    # =============================================================================
    # part of downloading hierarchy and product names from artgold
    # =============================================================================



    print("\nHierarchy to item_sold started....\n")

    conn = pyodbc.connect(
        ODBC_CONN, autocommit=True, Trusted_Connection="yes"
    )


    item_sold_df=pd.read_parquet(
        item_sold_dir
        )

    products = item_sold_df[['country', 'tpnb']].drop_duplicates()
    
    products = products.groupby(["country"])['tpnb'].apply(lambda s: s.tolist()).to_dict()


    for key, value in products.items():
        #print value
        print(key, len([item for item in value if item]))

    df2 = pd.DataFrame()

    for k, v in products.items():
                        
        s = list()
        
        for x in v:
                    
            s.append(str(x))
            tpnbs = ",".join(s)
        
        sql = """ SELECT 
                        mstr.cntr_code AS country,
                        mstr.slad_tpnb AS tpnb,
                        mstr.dmat_div_des_en AS division,
                        cast(mstr.dmat_div_code as INT) as DIV_ID,
                        mstr.dmat_dep_des_en AS department,
                        cast(mstr.dmat_dep_code as INT) as DEP_ID,
                        mstr.dmat_sec_des_en AS section,
                        cast(mstr.dmat_sec_code as INT) as SEC_ID,
                        mstr.dmat_grp_des_en AS group,
                        cast(mstr.dmat_grp_code as INT) as GRP_ID,
                        mstr.dmat_sgr_des_en AS subgroup,
                        cast(mstr.dmat_sgr_code as INT) as SGR_ID,
                        mstr.slad_long_des as product_name,
                        supl.dmsup_long_des as supplier_name
                FROM
                        DM.dim_artgld_details mstr
                LEFT JOIN
                        dw.dim_suppliers supl
                ON
                        supl.dmsup_cntr_id = mstr.cntr_id 
                AND
                        supl.dmsup_id = mstr.slad_dmsup_id
                WHERE 
                        slad_tpnb in ({tpnbs}) 
                AND     
                        cntr_code = '{k}' 
                AND     
                        dmat_sgr_des_en <> "Do not use"
                GROUP BY 
                        mstr.cntr_code,
                        mstr.slad_tpnb,
                        mstr.dmat_div_des_en,
                        mstr.dmat_div_code,
                        mstr.dmat_dep_des_en,
                        mstr.dmat_dep_code,
                        mstr.dmat_sec_des_en,
                        mstr.dmat_sec_code,
                        mstr.dmat_grp_des_en,
                        mstr.dmat_grp_code,
                        mstr.dmat_sgr_des_en,
                        mstr.dmat_sgr_code,
                        mstr.slad_long_des,
                        supl.dmsup_long_des
                        
                        """.format(
            tpnbs=tpnbs, k=k
        )

        art_gold = pd.read_sql(sql, conn)
        df2 = pd.concat([df2, art_gold])
        print(f"\nHierarchy part done with {k}\n")
        
    conn.close()   
    df2['tpnb'] = df2['tpnb'].astype("int")


    cols = ['weight', 'case_capacity']
    for x in cols:    
        item_sold_df[x] = item_sold_df[x].fillna(item_sold_df.groupby(['country', 'pmg'], observed=True)[x].transform("mean"))

    item_sold_df = item_sold_df.merge(df2, on=['country', 'tpnb'], how='left')

    hier_cols_str = ['division',
                      'department','section',
                      'group','subgroup',
                      'product_name']

    hier_cols_int = ['DIV_ID','DEP_ID',
                      'SEC_ID','GRP_ID',
                      'SGR_ID',]

    item_sold_df[hier_cols_str] = item_sold_df[hier_cols_str].replace(np.nan,"no_data")
    item_sold_df[hier_cols_int] = item_sold_df[hier_cols_int].replace(np.nan,0)

    item_sold_df = rmf.optimize_objects(rmf.optimize_types(item_sold_df))

    # item_sold_df.to_parquet(
    #     item_sold_dir + "_hier"
    #     )
    return item_sold_df
 

