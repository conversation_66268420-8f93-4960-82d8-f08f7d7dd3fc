import pyodbc
import pandas as pd
import polars as pl

import time

start = time.time()


conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()

# input_df =pl.read_excel(
#     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_07\Stolen_products_w14w27_2023.xlsx"
#     ,engine="calamine").to_pandas()


input_df = pd.read_clipboard()

products = a[['country', 'tpnb']].drop_duplicates()
products = a[['country', 'tpnb']].drop_duplicates().groupby(["country"],observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()


# products = {"SK":[2002020322517, 2002020319280, 2002020312286]}
for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))

df2 = pl.DataFrame()



for k, v in products.items():
                    
    s = list()
    
    for x in v:
                
        s.append(str(x))
        tpnbs = ",".join(s)
    
    sql = """ SELECT 
                    cntr_code AS country,
                    cast(slad_tpnb AS INT) AS tpnb,
                    hier.pmg as pmg,
                    slad_tpn AS tpn,
                    slad_unit AS unit_type,
                    slad_case_size AS case_capacity,
                    slad_net_weight AS weight,
                    dmat_div_des_en AS division,
                    cast(dmat_div_code as INT) as DIV_ID,
                    dmat_dep_des_en AS department,
                    cast(dmat_dep_code as INT) as DEP_ID,
                    dmat_sec_des_en AS section,
                    cast(dmat_sec_code as INT) as SEC_ID,
                    dmat_grp_des_en AS group,
                    cast(dmat_grp_code as INT) as GRP_ID,
                    dmat_sgr_des_en AS subgroup,
                    cast(dmat_sgr_code as INT) as SGR_ID,
                    slad_long_des as product_name
            FROM
                    DM.dim_artgld_details mstr
                    
            JOIN
                    tesco_analysts.hierarchy_spm hier ON mstr.dmat_div_code = LPAD(hier.div_code,4,"0") 
                    AND mstr.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
                    AND mstr.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
                    AND mstr.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
                    AND mstr.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
            WHERE 
                    slad_tpnb in ({tpnbs}) 
            AND     
                    cntr_code = '{k}' 
            AND     
                    dmat_sgr_des_en <> "Do not use"
            GROUP BY 
                    cntr_code,
                    slad_tpnb,
                    hier.pmg,
                    slad_tpn,
                    slad_unit,
                    slad_case_size,
                    slad_net_weight,
                    dmat_div_des_en,
                    dmat_div_code,
                    dmat_dep_des_en,
                    dmat_dep_code,
                    dmat_sec_des_en,
                    dmat_sec_code,
                    dmat_grp_des_en,
                    dmat_grp_code,
                    dmat_sgr_des_en,
                    dmat_sgr_code,
                    slad_long_des
                    
                    """.format(
        tpnbs=tpnbs, k=k
    )
    

    
    

    df2 =pl.concat([df2, pl.read_database(sql, conn)])
    # art_gold = pd.read_sql(sql, conn)

    
print(time.time() - start)













start = '20250701'
end = '20250720'

rec_4 = f"""



WITH ors_data AS (
    /* First CTE - Simplified and more efficient version */
    SELECT
        concat(substr(fw.dmtm_fw_code, 2, 4), substr(fw.dmtm_fw_code, 7, 2)) AS upl_date,
        art.cntr_code AS country,
        str.slsp_dmst_code AS store,
        SUBSTR(str.slsp_dmst_code, 2, 4) AS store_om,
        str.slsp_name AS store_desc,
        art.slad_tpn AS tpn,
        art.slad_tpnb AS tpnb,
        art.slad_long_des AS art_des,
        art.own_brand AS own_brand,
        CASE WHEN sl.tpnb IS NULL THEN 'N' ELSE 'Y' END AS starline,
        hier.pmg as pmg,
        art.dmat_div_code AS divs,
        art.dmat_div_des_en AS division,
        art.dmat_dep_code AS dept,
        art.dmat_dep_des_en AS department,
        art.dmat_sec_code AS sect,
        art.dmat_sec_des_en AS section,
        art.dmat_grp_code AS grp,
        art.dmat_grp_des_en AS group,
        art.dmat_sgr_code AS sgr,
        art.dmat_sgr_des_en AS subgroup,
        supp.dmsup_code AS supp_om,
        supp.dmsup_long_des AS supp_des,
        ors.orsor_code AS orsor_code,
        'NA' AS supp_po_no,
        --ors.orsor_type AS orsor_type,
        
        (CASE
            WHEN ors.orsor_type = "A"
                THEN 'PBL'
            WHEN ors.orsor_type = "T"
                THEN 'PBS'
            ELSE ors.orsor_type
        END) AS orsor_type,
        
        
        (CASE
            WHEN ors.orsor_pack_qty IS NULL
                THEN 1
            ELSE ors.orsor_pack_qty
        END) AS orsor_pack_qty,
        
        (CASE
            WHEN ors.orsor_sku_percar IS NULL
                THEN 1
            ELSE ors.orsor_sku_percar
        END) AS orsor_sku_percar,
        

        CAST(ors.orsor_date AS DATE) AS orsor_date,
        CAST(ors.orsor_exp_deliv AS DATE) AS orsor_exp_deliv,
        NVL(ors.orsor_or_quantity, 0) + NVL(ors.orsor_cancelled_quantity, 0) AS orsor_or_quantity,
        ors.orsor_tops_original_quantity AS tops_orig_qty,
        ors.orsor_tops_shortened_quantity AS tops_short_qty,
        ors.orsor_status AS orsor_status,
        ors.orsor_id AS orsor_id,
        ors.orsor_dmat_id AS orsor_dmat_id,
        ors.orsor_dmst_id_src AS orsor_dmst_id_src,
        ors.orsor_cntr_id AS orsor_cntr_id,
        ors.orsor_received_quantity AS orsor_received_quantity
    FROM
        dw.ors_orders ors
    JOIN
        dm.dim_artgld_details art
        ON  ors.orsor_cntr_id = art.cntr_id 
        AND ors.orsor_dmat_id = art.slad_dmat_id
    JOIN
        dm.dim_suppliers supp
        ON  art.slad_dmsup_id = supp.dmsup_id 
        AND art.cntr_id = supp.cntr_id
    JOIN
        dw.sl_store_params str
        ON  ors.orsor_cntr_id = str.slsp_cntr_id 
        AND ors.orsor_dmst_id_trg = str.slsp_dmst_id
    JOIN
        dm.dim_time_d fw
        ON  CAST(fw.dmtm_value AS DATE) = CAST(ors.orsor_exp_deliv AS DATE)
    LEFT JOIN
        sch_analysts.tbl_ce_eva sl
        ON  sl.tpnb = art.slad_tpnb 
        AND sl.country = art.cntr_code
        
    JOIN
        tesco_analysts.hierarchy_spm hier ON art.dmat_div_code = LPAD(hier.div_code,4,"0") 
        AND art.dmat_dep_code = LPAD(hier.dep_code,4,"0") 
        AND art.dmat_sec_code = LPAD(hier.sec_code,4,"0") 
        AND art.dmat_grp_code = LPAD(hier.grp_code,4,"0") 
        AND art.dmat_sgr_code = LPAD(hier.sgr_code,4,"0") 
    WHERE
        ors.orsor_cntr_id = '4'
        AND ors.part_col BETWEEN '{start}' AND '{end}'
        AND orsor_type in ('A', 'T', 'DTS')
        --AND (str.slsp_dmst_code = 44070 OR str.slsp_dmst_code IS NULL) -- Added store filter early
        --AND str.slsp_dmst_code in (41042)
        AND art.slad_tpnb in (105381925, 111267649)
        --AND art.dmat_sgr_des_en = 'gift_packs'
),

rec_data AS (
    /* Second CTE - For receiving data with early filtering for rec_acc_quant > 0 */
    SELECT
        r.orsrec_cntr_id AS cntr_id,
        r.orsrec_orsor_id AS orsor_id,
        r.orsrec_dmat_id AS dmat_id,
        sum(r.orsrec_quant) AS rec_qty,
        sum(NVL(r.orsrec_acc_quant, 0)) AS rec_acc_quant
    FROM
        dw.ors_receivings r
    WHERE
        r.orsrec_cntr_id in (1,2,3,4)
        AND r.part_col BETWEEN '{start}' AND '{end}'
        AND NVL(r.orsrec_acc_quant, 0) > 0 -- Filter for positive receivings only
    GROUP BY
        r.orsrec_cntr_id,
        r.orsrec_orsor_id,
        r.orsrec_dmat_id
),

ordrec_data AS (
    /* Third CTE - Combines orders and receivings */
    SELECT
        ors.upl_date AS upl_date,
        ors.country AS country,
        ors.store AS store,
        ors.store_om AS store_om,
        ors.store_desc AS store_desc,
        ors.tpn AS tpn,
        ors.tpnb AS tpnb,
        ors.art_des AS art_des,
        ors.own_brand AS own_brand,
        ors.starline AS starline,
        ors.pmg as pmg,
        ors.divs AS divs,
        ors.division as division,
        ors.dept AS dept,
        ors.department as department,
        ors.sect AS sect,
        ors.section as section,
        ors.grp AS grp,
        ors.group as group,
        ors.sgr AS sgr,
        ors.subgroup as subgroup,
        ors.supp_om AS supp_om,
        ors.supp_des AS supp_des,
        ors.orsor_code AS orsor_code,
        ors.supp_po_no AS supp_po_no,
        ors.orsor_type AS orsor_type,
        ors.orsor_pack_qty AS orsor_pack_qty,
        ors.orsor_sku_percar AS orsor_sku_percar,
        ors.orsor_date AS orsor_date,
        ors.orsor_exp_deliv AS orsor_exp_deliv,
        ors.orsor_or_quantity AS orsor_or_quantity,
        ors.tops_orig_qty AS tops_orig_qty,
        ors.tops_short_qty AS tops_short_qty,
        (CASE
            WHEN ors.orsor_pack_qty >= ors.orsor_sku_percar
                THEN COALESCE(rec.rec_acc_quant, 0)
            ELSE (COALESCE(rec.rec_acc_quant, 0))/NULLIF(ors.orsor_sku_percar, 0)
        END) AS rec_case,
        (CASE
            WHEN ors.orsor_pack_qty >= ors.orsor_sku_percar
                THEN (COALESCE(rec.rec_acc_quant, 0))*ors.orsor_pack_qty
            ELSE COALESCE(rec.rec_acc_quant, 0) 
        END) AS rec_unit,
        COALESCE(ors.orsor_received_quantity, 0) AS planned_shipped_qty,
        ors.orsor_status AS orsor_status,
        ors.orsor_id AS orsor_id,
        ors.orsor_dmat_id AS orsor_dmat_id,
        ors.orsor_dmst_id_src AS orsor_dmst_id_src,
        ors.orsor_cntr_id AS orsor_cntr_id
    FROM
        ors_data ors
    JOIN  -- Changed from LEFT JOIN to INNER JOIN for receivings
        rec_data rec
        ON  rec.cntr_id = ors.orsor_cntr_id 
        AND rec.orsor_id = ors.orsor_id
    -- No need for rec_case > 0 filter here as we're using INNER JOIN with rec_data
    -- and we've already filtered rec_data for positive receivings
),

base_data AS (
    SELECT
        ors.upl_date AS fw,
        ors.country AS country,
        ors.store AS store,
        ors.store_om AS store_om,
        ors.store_desc AS store_desc,
        ors.tpn AS tpn,
        ors.tpnb AS tpnb,
        ors.art_des AS art_des,
        ors.own_brand AS own_brand,
        ors.starline AS starline,
        -- For DTS order type, allow dc to be NULL, otherwise use the COALESCE logic
        CASE 
            WHEN ors.orsor_type = 'DTS' THEN COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code)
            ELSE COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code)
        END AS dc,
        CASE 
            WHEN ors.orsor_type = 'DTS' THEN COALESCE(vp_dc.ph_name, direct_dc.slsp_name)
            ELSE COALESCE(vp_dc.ph_name, direct_dc.slsp_name)
        END AS dc_desc,
        ors.orsor_id AS orsor_id,
        ors.orsor_status AS ord_status,
        ors.pmg as pmg,
        ors.divs AS divs,
        ors.division as division,
        ors.dept AS dept,
        ors.department as department,
        ors.sect AS sect,
        ors.section as section,
        ors.grp AS grp,
        ors.group as group,
        ors.sgr AS sgr,
        ors.subgroup as subgroup,
        ors.supp_om AS supp_om,
        ors.supp_des AS supp_des,
        ors.orsor_code AS store_transfer_no,
        ors.supp_po_no AS supp_po_no,
        ors.orsor_type AS ord_type,
        goart.goar_order_group AS og,
        goart.goar_distribution_group_code AS dg,
        ors.orsor_pack_qty AS pack_qty1,
        ors.orsor_sku_percar AS pack_qty2,
        ors.orsor_date AS ord_date,
        ors.orsor_exp_deliv AS exp_del_date,
        ors.orsor_or_quantity AS ord_qty,
        ors.tops_orig_qty AS tops_orig_qty,
        ors.tops_short_qty AS tops_short_qty,
        ors.rec_case AS rec_case,
        ors.rec_unit AS rec_unit,
        ors.planned_shipped_qty AS planned_shipped_qty,
        ors.orsor_cntr_id AS cntr_id
    FROM
        ordrec_data ors
    LEFT JOIN
        dm.go_article goart
        ON  ors.orsor_cntr_id = goart.cntr_id 
        AND ors.orsor_dmat_id = goart.goar_dmat_id
    -- Join for virtual->physical DC relationship
    LEFT JOIN
        (
            SELECT
                ph.slsp_dmst_id AS ph_id,
                ph.slsp_dmst_code AS ph_code,
                ph.slsp_name AS ph_name,
                vr.slsp_dmst_id AS vr_id,
                ph.slsp_cntr_id AS cntr
            FROM
                dw.sl_store_params ph
            JOIN
                dw.sl_store_params vr
                ON  ph.slsp_dmst_id = vr.slsp_phys_dmst_id 
                AND ph.slsp_cntr_id = vr.slsp_cntr_id
            WHERE
                ph.slsp_dmst_code != 'null'
        ) vp_dc
        ON  ors.orsor_dmst_id_src = vp_dc.vr_id 
        AND ors.orsor_cntr_id = vp_dc.cntr
    -- Join for direct DC relationship
    LEFT JOIN
        dw.sl_store_params direct_dc
        ON  ors.orsor_cntr_id = direct_dc.slsp_cntr_id 
        AND ors.orsor_dmst_id_src = direct_dc.slsp_dmst_id
    -- Modified WHERE clause to allow DTS records even if dc is NULL
    WHERE ors.orsor_type = 'DTS' OR COALESCE(vp_dc.ph_code, direct_dc.slsp_dmst_code) IS NOT NULL
)


SELECT * FROM base_data;


"""



a = pl.read_database(rec_4, conn).to_pandas()




import pyodbc
import pandas as pd




conn = pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
)
cursor = conn.cursor()


df = pd.DataFrame()





for x in ["HU", "SK", "CZ"]:

    srd = f"""
    
    
    -- Flop Table -- -- START --
    with Flop_POG_1 AS (
    	SELECT DISTINCT
    		f.dbkey AS FlopID
    		, f.desc12 AS Store_Number
    		, f.desc11 AS Store_Name
    		, f.desc2 AS Store_Format
    		, fs.DBParentPlanogramKey
    		, fs.dbkey AS FS_DBkey
    		, fs.SegmentStart
    		, fs.SegmentEnd	
    		, f.int_cntr_code
    		FROM stg_ikb.ix_flr_floorplan AS f
    			LEFT JOIN stg_ikb.ix_flr_section AS fs ON (fs.dbparentfloorplankey = f.dbkey AND fs.int_cntr_code = f.int_cntr_code)
    		WHERE f.dbstatus = 1 
            AND f.int_cntr_code = '{x}'
    		),
    -- Flop Table -- -- END --
    
    -- POG Table -- -- START --
    Pog_Table_0 AS (
    	SELECT DISTINCT
    		p.desc2 AS Displaygroup
    		, p.desc24 AS Displaygroup_description	
    		, p.Dbkey AS POG_ID
    		, p.name AS POG_NAME
    		, p.dbversiONkey
    		, p.DBStatus AS POG_DBStatus
    		, p.int_cntr_code AS Country
    		, p.NumberOfProductsAllocated
    		, p.desc8 AS NumberOfMods 
    		FROM stg_ikb.ix_spc_planogram AS p 
    		WHERE (p.dbstatus not in ('4','3'))	AND CONCAT(p.dbversiONkey,p.int_cntr_code) in (SELECT DISTINCT CONCAT(dbparentplanogramkey,int_cntr_code) as A FROM Flop_POG_1)),
    -- POG Table -- -- END --
    
    -- Darab Table -- -- START --
    Darab_1 AS (
    	SELECT DISTINCT 
    		p_help.POG_ID
    		, p_help.POG_NAME
    		, pos.segment
    		, fx.x
    		, fx.y
    		, p_help.Country
    		, COUNT(CONCAT(p_help.POG_ID, pos.segment, fx.x, fx.y)) AS Darab
    		FROM  Pog_Table_0 AS p_help
    			LEFT JOIN stg_ikb.ix_spc_position AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.int_cntr_code = p_help.Country) 
    			LEFT JOIN stg_ikb.ix_spc_fixture AS fx ON (p_help.POG_ID=fx.DBParentPlanogramKey AND pos.DBparentfixturekey=fx.dbkey AND fx.int_cntr_code = p_help.Country) 
    		GROUP BY p_help.POG_ID
    			, p_help.POG_NAME
    			, pos.segment
    			, fx.x
    			, fx.y
    			, p_help.Country),
    -- Darab Table -- -- END --
    
    -- PositiON Table -- -- START --
    Pos_Table_1 AS (
    	SELECT DISTINCT 
    		pos.segment
    		, pos.hfacings
    		, pos.vfacings
    		, pos.dfacings
    		, pos.merchstyle
    		, pos.capacity
    		, pos.dbparentplanogramkey
    		, pos.dbparentfixturekey
    		, pos.dbparentproductkey
    		, pos.int_cntr_code AS Country
    		FROM  Pog_Table_0 AS p_help
    			LEFT JOIN stg_ikb.ix_spc_position AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.int_cntr_code = p_help.Country)),
    -- PositiON Table -- -- END --
    
    -- Pog1 Table -- -- START --
    Pog_Table_1 AS (
    	SELECT DISTINCT 
    		p_help.Displaygroup
    		, p_help.Displaygroup_description
    		, p_help.POG_ID
    		, p_help.POG_Name 
    		, p_help.NumberOfProductsAllocated
    		, p_help.NumberOfMods
    		, pos.segment
    		, fx.dbkey as Fixture_dbkey
    		, fx.name AS Fixture_name
    		, fx.depth AS Fixture_depth 
    		, fx.Width AS Fixture_width
    		, fx.x AS Fixture_X_position
    		, fx.y AS Fixture_Y_position
    		, pr.id AS Product_id
    		, pr.Product_Name
    		, pos.hfacings
    		, pos.vfacings 
    		, pos.dfacings
    		, pos.merchstyle
    		, pos.capacity AS Position_Capacity
    		, CASE 
      			WHEN pos.merchstyle=0 THEN 'Unit'
      			WHEN pos.merchstyle=1 THEN 'Tray'
      			WHEN pos.merchstyle=2 THEN 'Case'
      			WHEN pos.merchstyle=3 THEN 'Display'
      			WHEN pos.merchstyle=4 THEN 'Alternate'
      			WHEN pos.merchstyle=5 THEN 'Loose'
      			WHEN pos.merchstyle=6 THEN 'Log Stack' 
      			ELSE 'NULL' 
      			END AS merchstyle_string
    		, pr.desc18 AS brand
    		, pr.desc23 AS ownbrand  ---desc26 volt
    		, pr.Height
    		, pr.Width 
    		, pr.Depth 
    		, pr.TrayHeight 
    		, pr.TrayWidth 
    		, pr.TrayDepth 
    		, pr.TrayNumberHigh 
    		, pr.TrayNumberWide 
    		, pr.TrayNumberDeep 
    		, pr.TrayTotalNumber  
    		, pr.CASeHeight
    		, pr.CASeWidth 
    		, pr.CASeDepth 
    		, pr.CASeNumberHigh 
    		, pr.CASeNumberWide 
    		, pr.CASeNumberDeep
    		, pr.CASeTotalNumber
    		, perf.capacity AS Capacity_ON_Planogram
    		, p_help.Country
    		FROM Pog_Table_0 AS p_help
    			LEFT JOIN Pos_Table_1 AS pos ON (p_help.POG_ID=pos.dbparentplanogramkey AND pos.Country = p_help.Country) 
    			LEFT JOIN stg_ikb.ix_spc_product AS pr ON (pr.dbkey=pos.dbparentproductkey AND pr.int_cntr_code = p_help.Country)
    			LEFT JOIN stg_ikb.ix_spc_fixture AS fx ON (p_help.POG_ID=fx.DBParentPlanogramKey AND pos.DBparentfixturekey=fx.dbkey AND fx.int_cntr_code = p_help.Country)
    			LEFT JOIN stg_ikb.ix_spc_performance AS perf ON (p_help.POG_ID=perf.DBParentPlanogramKey AND pr.dbkey=perf.DBParentProductKey AND perf.int_cntr_code = p_help.Country)),
    -- Pog1 Table -- -- END --
    
    -- Flop2 Table -- -- START --
    Flop2 AS (
    	SELECT DISTINCT 
    		tabla.FlopID 
    		, tabla.Store_Number
    		, tabla.POG_ID
    		, IF(tabla.Z_Szegment = Tabla.NumberOfMods,1,
    			IF(tabla.Z_Szegment > Tabla.NumberOfMods,
    			IF((tabla.Z_Szegment /Tabla.NumberOfMods)=(Round(tabla.Z_Szegment /Tabla.NumberOfMods,1)),tabla.Z_Szegment /Tabla.NumberOfMods,0),0)) AS X_POG_NUM
    		, tabla.int_cntr_code
    		FROM (SELECT DISTINCT
    			tabla.FlopID
    			, tabla.Store_Number
    			, tabla.POG_ID
    			, tabla.NumberOfMods
    			, SUM(IF( tabla.SegmentStart=0 AND tabla.SegmentEnd=0,tabla.NumberOfMods
    			  ,	IF(tabla.SegmentStart=1 AND tabla.SegmentEnd=tabla.NumberOfMods,tabla.NumberOfMods
    			  , IF(tabla.SegmentStart=tabla.SegmentEnd,1,tabla.SegmentEnd- IF(tabla.SegmentStart>1,tabla.SegmentStart-1,0))))) 
    		      AS Z_Szegment 
    			, tabla.int_cntr_code
    			FROM (SELECT DISTINCT
    				Flop_POG_1.FlopID
    				, Flop_POG_1.fs_dbkey
    				, Flop_POG_1.Store_Number
    				, Pog_Table_1.POG_ID
    				, Pog_Table_1.NumberOfMods
    				, Flop_POG_1.SegmentStart
    				, Flop_POG_1.SegmentEnd
    				, Flop_POG_1.int_cntr_code
    				FROM Flop_POG_1 
    					LEFT JOIN Pog_Table_1 ON (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID AND Flop_POG_1.int_cntr_code=Pog_Table_1.Country)) AS tabla
    			GROUP by tabla.FlopID
    			, tabla.Store_Number
    			, tabla.POG_ID
    			, tabla.NumberOfMods
    			, tabla.int_cntr_code
    			) AS tabla)
    -- Flop2 Table -- -- END --
    
    -- Final Table -- -- START --
    SELECT DISTINCT
    		Flop_POG_1.int_cntr_code AS Country
    		, Flop_POG_1.Store_Number
    		, Flop_POG_1.Store_Name
    		, Flop_POG_1.Store_Format
    		, Pog_Table_1.Displaygroup
    		, Pog_Table_1.Displaygroup_description
    		, Pog_Table_1.POG_ID
    		, Pog_Table_1.POG_Name
    		, Pog_Table_1.segment AS Segmentnumber
    		, Pog_Table_1.Fixture_dbkey
    		, Pog_Table_1.Fixture_name
    		, Pog_Table_1.Fixture_depth
    		, Pog_Table_1.Fixture_width
    		, IF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring(Pog_Table_1.Fixture_name, 1, 3) = 'Rak'
    		  ,	IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 AND Pog_Table_1.Fixture_width = 80)
    		  ,	IF(Darab_1.Darab >1,'Split_ON_Half_Pallet', 'Half_Pallet')
    		  , IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 AND Pog_Table_1.Fixture_width = 80)
    		  ,	If(Darab_1.Darab >1, 'Split_Pallet','Pallet'),'')),'') 
    		    AS Pallet_info
    		, IF(substring(Pog_Table_1.Fixture_name, 1, 3) = 'PAL' or substring (Pog_Table_1.Fixture_name, 1, 3) = 'Rak'
    		  ,	IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =60) or (Pog_Table_1.Fixture_depth = 60 AND Pog_Table_1.Fixture_width = 80)
    		  ,	IF(Darab_1.Darab >1,'Split_ON_Half_Pallet', 'Half_Pallet')
    		  , IF((Pog_Table_1.Fixture_depth = 80 AND Pog_Table_1.Fixture_width =120) or (Pog_Table_1.Fixture_depth= 120 AND Pog_Table_1.Fixture_width = 80)
    		  ,	If(Darab_1.Darab >1, 'Split_Pallet','Pallet'),Pog_Table_1.merchstyle_string)),Pog_Table_1.merchstyle_string) 
    		    AS INFO
    		, Pog_Table_1.Product_id
    		, Pog_Table_1.Product_Name
    		, Pog_Table_1.hfacings AS Position_HFacing
    		, Pog_Table_1.vfacings AS Position_VFacing 
    		, Pog_Table_1.dfacings AS Position_DFacing
    		, Pog_Table_1.merchstyle AS merchstyle_ID
    		, Pog_Table_1.merchstyle_string
    		, Pog_Table_1.brand
    		, Pog_Table_1.ownbrand
    		, Pog_Table_1.Height
    		, Pog_Table_1.Width
    		, Pog_Table_1.Depth
    		, Pog_Table_1.TrayHeight
    		, Pog_Table_1.TrayWidth
    		, Pog_Table_1.TrayDepth
    		, Pog_Table_1.TrayNumberHigh
    		, Pog_Table_1.TrayNumberWide
    		, Pog_Table_1.TrayNumberDeep
    		, Pog_Table_1.TrayTotalNumber
    		, Pog_Table_1.CaseHeight
    		, Pog_Table_1.CaseWidth 
    		, Pog_Table_1.CaseDepth 
    		, Pog_Table_1.CaseNumberHigh
    		, Pog_Table_1.CaseNumberWide 
    		, Pog_Table_1.CaseNumberDeep
    		, Pog_Table_1.CaseTotalNumber
    		, artrp.dmat_div_code AS Division
    		, artrp.dmat_dep_code AS Department
    		, artrp.dmat_sec_code AS Section
    		, artrp.dmat_grp_code AS Group
    		, artrp.dmat_sgr_code AS Subgroup
    		, Pog_Table_1.NumberOfProductsAllocated
    		, Pog_Table_1.Position_Capacity
    		, fl.X_POG_NUM AS Planogram_on_Store
    		, Pog_Table_1.Position_Capacity * fl.X_POG_NUM AS Position_Capacity_X_Planogram_on_Store
    		FROM Flop_POG_1 
    			JOIN Pog_Table_1 ON (Flop_POG_1.DBParentPlanogramKey=Pog_Table_1.POG_ID AND Flop_POG_1.int_cntr_code=Pog_Table_1.Country) 
    			JOIN Darab_1 ON (Pog_Table_1.POG_ID=Darab_1.POG_ID AND Pog_Table_1.segment=Darab_1.segment	AND Pog_Table_1.Fixture_X_position=Darab_1.x AND Pog_Table_1.Fixture_Y_position=Darab_1.y AND Flop_POG_1.int_cntr_code=Darab_1.Country) 
    			JOIN Flop2 AS FL ON (FL.Store_Number=Flop_POG_1.Store_Number AND FL.POG_ID=Flop_POG_1.DBParentPlanogramKey AND FL.int_cntr_code=Flop_POG_1.int_cntr_code) 
    			LEFT JOIN dm.dim_artrep_details AS artrp ON (Pog_Table_1.Product_id = artrp.slad_tpnb AND artrp.cntr_code = Pog_Table_1.Country);
    -- Final Table -- -- END --
    
    
    
    
    """
    
    df =pd.concat([df, pd.read_sql(srd, conn)])

    

    # df = pl.read_database(pl.concat([srd,df]), conn).to_pandas()
    print(f"Done with {x}\n")
    
    

    