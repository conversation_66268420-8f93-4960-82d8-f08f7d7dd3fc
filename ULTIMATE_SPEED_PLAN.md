# ULTIMATE SPEED OPTIMIZATION PLAN

## 🎯 **Current Performance Analysis**

**Current Results**: 22.2 minutes (1,331 seconds)
- 9 chunks × ~2.5 minutes per chunk
- Each chunk processes ~6-10M rows through complex business logic
- **The chunking is STILL the bottleneck** - we're doing 9× the work instead of 1× the work

## 🚀 **ULTIMATE OPTIMIZATION STRATEGY**

### **Phase 1: ELIMINATE CHUNKING COMPLETELY (Target: 5-8 minutes)**

**Root Problem**: We're still calling `Repl_Drivers_Calculation()` 9 times instead of 1 time.

**Solution**: Create a **SINGLE-PASS VECTORIZED VERSION** that processes all 52.6M rows at once.

#### **Key Changes**:
1. **Remove ALL chunking logic**
2. **Process the entire dataset in one operation**
3. **Optimize the core `Repl_Drivers_Calculation()` function**
4. **Use vectorized operations throughout**

### **Phase 2: VECTORIZE CORE OPERATIONS (Target: 3-5 minutes)**

**Bottlenecks Identified in `Repl_Drivers_Calculation()`**:

1. **Multiple DataFrame copies**: `Drivers = Repl_Dataset.copy()`
2. **Expensive groupby operations**: `Drivers.groupby(["store", "pmg"])`
3. **Multiple merge operations**: 3-4 merges with store_inputs
4. **Row-by-row calculations**: Hundreds of `np.where()` operations
5. **Categorical conversions**: `pd.Categorical()` operations

#### **Optimization Techniques**:

**A. Eliminate Unnecessary Copies**
```python
# BEFORE (Slow)
Drivers = Repl_Dataset.copy()  # 16GB copy!
Drivers = Drivers[Drivers.store.isin(stores)]

# AFTER (Fast)
# Work directly on filtered view, no copy
Drivers = Repl_Dataset[Repl_Dataset.store.isin(stores)]
```

**B. Pre-compute Expensive Operations**
```python
# BEFORE (Slow) - computed 9 times
for chunk in chunks:
    chunk_data['capacity_avg_pmg'] = chunk_data.groupby(["store", "pmg"])['shelfCapacity'].transform('mean')

# AFTER (Fast) - computed once
Repl_Dataset['capacity_avg_pmg'] = Repl_Dataset.groupby(["store", "pmg"])['shelfCapacity'].transform('mean')
```

**C. Vectorize Business Logic**
```python
# BEFORE (Slow) - multiple separate operations
Drivers["heavy"] = Drivers.weight * Drivers.case_capacity
Drivers["weight_selector"] = Drivers.weight * Drivers.case_capacity
Drivers["heavy"] = np.where(Drivers.weight_selector >= 5, 1, 0)
Drivers["light"] = np.where(Drivers.weight_selector < 5, 1, 0)

# AFTER (Fast) - single vectorized operation
weight_selector = Drivers.weight * Drivers.case_capacity
Drivers["heavy"] = (weight_selector >= 5).astype('int8')
Drivers["light"] = (weight_selector < 5).astype('int8')
```

### **Phase 3: PARALLEL PROCESSING (Target: 1-3 minutes)**

**Use multiprocessing to parallelize independent operations**:

```python
from multiprocessing import Pool
import numpy as np

def process_store_group(store_data):
    # Process each store's data independently
    return optimized_calculations(store_data)

# Split by stores and process in parallel
with Pool(processes=8) as pool:  # Use 8 CPU cores
    results = pool.map(process_store_group, store_groups)
```

### **Phase 4: MEMORY OPTIMIZATION (Target: 30 seconds - 2 minutes)**

**A. Use Efficient Data Types**
```python
# BEFORE (Slow)
Drivers["heavy"] = np.where(condition, 1, 0)  # Default int64

# AFTER (Fast)
Drivers["heavy"] = np.where(condition, 1, 0).astype('int8')  # 8x less memory
```

**B. In-Place Operations**
```python
# BEFORE (Slow)
Drivers = Drivers.replace(np.nan, 0)  # Creates copy

# AFTER (Fast)
Drivers.fillna(0, inplace=True)  # In-place operation
```

**C. Chunked Vectorization for Memory Control**
```python
# Process in memory-efficient chunks but vectorized
chunk_size = 5_000_000  # 5M rows at a time
for i in range(0, len(data), chunk_size):
    chunk = data.iloc[i:i+chunk_size]
    vectorized_operations(chunk)  # All operations on chunk at once
```

## 🔧 **IMPLEMENTATION PLAN**

### **Step 1: Create Single-Pass Version (Immediate - 60% speedup)**
- Remove all chunking from `Repl_Drivers_Calculation_TPN_optimized`
- Process entire 52.6M rows in single operation
- Expected: **8-10 minutes**

### **Step 2: Optimize Core Function (1-2 hours - 80% speedup)**
- Vectorize `Repl_Drivers_Calculation()` function
- Eliminate unnecessary copies and operations
- Pre-compute expensive groupby operations
- Expected: **4-6 minutes**

### **Step 3: Add Parallel Processing (2-3 hours - 90% speedup)**
- Split processing by stores or departments
- Use multiprocessing for independent calculations
- Expected: **2-3 minutes**

### **Step 4: Memory & Data Type Optimization (1 hour - 95% speedup)**
- Optimize data types (int8, float32 instead of int64, float64)
- Use in-place operations
- Memory-efficient vectorization
- Expected: **1-2 minutes**

## 📊 **EXPECTED PERFORMANCE IMPROVEMENTS**

| Phase | Technique | Current Time | Target Time | Speedup |
|-------|-----------|--------------|-------------|---------|
| **Baseline** | Current chunked approach | 22.2 min | - | - |
| **Phase 1** | Eliminate chunking | 22.2 min | 8-10 min | **2.2-2.8x** |
| **Phase 2** | Vectorize core operations | 8-10 min | 4-6 min | **2x** |
| **Phase 3** | Parallel processing | 4-6 min | 2-3 min | **2x** |
| **Phase 4** | Memory optimization | 2-3 min | 1-2 min | **1.5-2x** |
| **TOTAL** | **All optimizations** | **22.2 min** | **1-2 min** | **11-22x faster** |

## 🚀 **IMMEDIATE ACTION PLAN**

### **Quick Win (30 minutes implementation)**
1. **Remove chunking completely**
2. **Process all 52.6M rows in single operation**
3. **Expected result: 8-10 minutes** (60% improvement)

### **Medium Term (2-3 hours implementation)**
1. **Vectorize core business logic**
2. **Optimize data types and memory usage**
3. **Expected result: 3-5 minutes** (85% improvement)

### **Advanced (1 day implementation)**
1. **Add parallel processing**
2. **Complete memory optimization**
3. **Expected result: 1-2 minutes** (95% improvement)

## 🎯 **SUCCESS METRICS**

- **Phase 1 Success**: < 10 minutes (from 22.2 minutes)
- **Phase 2 Success**: < 5 minutes (from 22.2 minutes)
- **Phase 3 Success**: < 3 minutes (from 22.2 minutes)
- **Ultimate Success**: < 2 minutes (from 22.2 minutes)

## 🔧 **TECHNICAL IMPLEMENTATION**

### **Priority 1: Single-Pass Implementation**
```python
def Repl_Drivers_Calculation_TPN_ultimate():
    # NO CHUNKING - process all data at once
    print("🚀 ULTIMATE optimization: Processing all 52.6M rows in single operation...")
    
    # Direct processing without chunking
    Drivers_produce, Drivers = Repl_Drivers_Calculation_vectorized(
        Repl_Dataset,  # Full dataset, no chunks
        # ... all parameters
    )
    
    return Drivers, Drivers_produce
```

### **Priority 2: Vectorized Core Function**
```python
def Repl_Drivers_Calculation_vectorized():
    # Eliminate copies, use views
    # Pre-compute all groupby operations
    # Vectorize all business logic
    # Use efficient data types
    # In-place operations where possible
```

## 🎉 **EXPECTED OUTCOME**

**From 22.2 minutes to 1-2 minutes = 11-22x faster processing!**

Your replenishment model will go from taking nearly half an hour to completing in **under 2 minutes** with full optimization.

**Next Step**: Implement Phase 1 (single-pass processing) for immediate 60% speedup.
