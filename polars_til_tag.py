import pandas as pd
import polars as pl
import numpy as np

from tagging_logic_0311 import tagging_on_product, check_tag_values, tagging_old_model



weekdays_to_divide = 7


def Repl_Drivers_Calculation_polars_version(
    directory,
    Repl_Dataset,
    store_inputs,
    backstock_target,
    RC_Capacity_Ratio,
    shelf_trolley_cap_ratio_to_pallet,
    shelf_trolley_cap_ratio_to_rollcage,
    excel_inputs_f,
    MODULE_CRATES,
    TABLE_CRATES,
    FULFILL_TARGET,
    SALES_CYCLE,
    RC_CAPACITY,
    RC_DELIVERY,
    RC_VS_PAL_CAPACITY,
    only_tpn,
    tpnb_store,
    tpnb_country,
    selected_tpn,
    capping_shelves_ratio,
    stores,
    version,
    shelfService_gm,
    cases_to_replenish_only
):
    """
    Enhanced Polars-based implementation of drivers calculation.

    Key optimizations:
    1. Vectorized operations using Polars expressions
    2. Lazy evaluation where possible
    3. Memory-efficient data types
    4. Batch processing of all stores simultaneously
    5. Optimized joins and aggregations
    """

    from Replenishment_Model_Functions_25 import optimize_objects, optimize_types, optimize, optimize_ints, optimize_floats

    print("🔧 Starting Polars-optimized drivers calculation...")
    start_time = time.time()

    # Convert to Polars DataFrame if it's pandas with optimized schema
    if isinstance(Repl_Dataset, pd.DataFrame):
        print(f"📊 Converting pandas DataFrame ({len(Repl_Dataset):,} rows) to Polars...")
        # Optimize data types before conversion for better memory usage
        drivers = pl.from_pandas(Repl_Dataset, schema_overrides={
            'store': pl.Categorical,
            'day': pl.Categorical,
            'dep': pl.Categorical,
            'pmg': pl.Categorical,
            'country': pl.Categorical,
            'format': pl.Categorical,
            'division': pl.Categorical
        })
    else:
        drivers = Repl_Dataset.clone()

    print(f"✅ Data conversion completed. Processing {drivers.height:,} rows for {len(stores)} stores...")

    # Performance monitoring
    def log_progress(step_name, start_time):
        elapsed = time.time() - start_time
        print(f"⏱️  {step_name}: {elapsed:.2f}s")
        return time.time()

    step_time = time.time()

    # Filter by stores using is_in() method - this is much faster than pandas isin()
    drivers = drivers.filter(pl.col("store").is_in(stores))
    step_time = log_progress("Store filtering", step_time)
    
    # MU customization - conditional column update
    drivers = drivers.with_columns(
        pl.when(pl.col("mu") > 0)
        .then(pl.col("pallet_capacity") / 2)
        .otherwise(pl.col("pallet_capacity"))
        .alias("pallet_capacity")
    )
    step_time = log_progress("MU customization", step_time)

    # Drop columns
    drivers = drivers.drop(["ownbrand", "checkout_stand_flag"])

    # Fill foil column where values are 0
    drivers = drivers.with_columns(
        pl.when(pl.col("foil") == 0)
        .then(pl.lit(1))
        .otherwise(pl.col("foil"))
        .alias("foil")
    )
    step_time = log_progress("Initial transformations", step_time)

    # Filter produce data
    drivers_produce = drivers.filter(
        (pl.col("dep") == "PRO") &
        (pl.col("pmg") != "PRO16") &
        (pl.col("pmg") != "PRO19")
    )

    # Get unique pmg values to delete
    pro_to_del = drivers_produce.select("pmg").unique().to_series().to_list()

    # Filter out produce and HDL01
    drivers = drivers.filter(
        (~pl.col("pmg").is_in(pro_to_del)) &
        (pl.col("pmg") != "HDL01")
    )
    step_time = log_progress("Produce filtering", step_time)
    
    if drivers.height > 0:  # Polars uses .height instead of len()
        
        # Shelf capacity customization
        weekday_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        
        # Convert day to categorical and sort
        drivers = drivers.with_columns(
            pl.col("day").cast(pl.Categorical(ordering="lexical"))
        ).sort(["store", "pmg", "tpnb", "day"])
        
        # Calculate capacity average by group
        drivers = drivers.with_columns(
            pl.col("shelfCapacity")
            .mean()
            .over(["store", "pmg"])
            .alias("capacity_avg_pmg")
        )
        
        # Adjust capacity_avg_pmg where it's less than 5
        drivers = drivers.with_columns(
            pl.when(pl.col("capacity_avg_pmg") < 5)
            .then(pl.lit(8))
            .otherwise(pl.col("capacity_avg_pmg"))
            .alias("capacity_avg_pmg")
        )
        
        # Fill nulls with 0 (equivalent to replace(np.nan, 0))
        drivers = drivers.fill_null(0)
        
        # Update shelfCapacity
        drivers = drivers.with_columns(
            pl.when((pl.col("shelfCapacity") == 0) | (pl.col("shelfCapacity") == 1))
            .then(pl.col("capacity_avg_pmg"))
            .otherwise(pl.col("shelfCapacity"))
            .alias("shelfCapacity")
        )
        
        # Update stock
        drivers = drivers.with_columns(
            pl.when((pl.col("stock") == 0) & (pl.col("sold_units") > 0))
            .then(pl.col("sold_units"))
            .otherwise(pl.col("stock"))
            .alias("stock")
        )
        
        # Weight customization
        drivers = drivers.with_columns([
            (pl.col("weight") * pl.col("case_capacity")).alias("heavy"),
            (pl.col("weight") * pl.col("case_capacity")).alias("weight_selector")
        ])
        
        drivers = drivers.with_columns([
            pl.when(pl.col("weight_selector") >= 5)
            .then(pl.lit(1))
            .otherwise(pl.lit(0))
            .cast(pl.Int8)
            .alias("heavy"),
            
            pl.when(pl.col("weight_selector") < 5)
            .then(pl.lit(1))
            .otherwise(pl.lit(0))
            .cast(pl.Int8)
            .alias("light")
        ])
        
        # Drop weight_selector
        drivers = drivers.drop("weight_selector")
        
        # Broken Items case cap
        drivers = drivers.with_columns(
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.col("case_capacity"))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("Broken Items")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.col("cases_delivered") - 1)
            .otherwise(pl.col("cases_delivered"))
            .cast(pl.Float32)
            .alias("cases_delivered_on_sf")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("cases_delivered_on_sf") < 0)
            .then(pl.lit(0))
            .otherwise(pl.col("cases_delivered_on_sf"))
            .alias("cases_delivered_on_sf")
        )
        
        # Setup icream_nsrp
        drivers = drivers.with_columns(
            pl.when(pl.col("icream_nsrp") > 0)
            .then(pl.col("icream_nsrp") + pl.col("nsrp"))
            .otherwise(pl.col("nsrp"))
            .alias("nsrp")
        )
        
        # SRP / full_pallet / mu customizing
        flag_columns = ['srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp', 'single_pick']
        for col in flag_columns:
            drivers = drivers.with_columns(
                pl.when((pl.col(col) > 0) & (pl.col("broken_case_flag") == 1))
                .then(pl.lit(0))
                .otherwise(pl.col(col))
                .alias(col)
            )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("broken_case_flag") == 1)
            .then(pl.lit(1))
            .otherwise(pl.col("nsrp"))
            .alias("nsrp")
        )
        
        # Single pick customization
        single_pick_columns = ['nsrp', 'srp', 'split_pallet', 'full_pallet', 'mu', 'icream_nsrp']
        for col in single_pick_columns:
            drivers = drivers.with_columns(
                pl.when(pl.col("single_pick") > 0)
                .then(pl.lit(0))
                .otherwise(pl.col(col))
                .alias(col)
            )
        
        # Backroom calculations
        drivers = drivers.with_columns(
            pl.when(pl.col("backroom_flag") == 1)
            .then(pl.col("sold_units_dotcom") / pl.col("case_capacity"))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("backroom_cases_dotcom")
        )
        
        # Handle inf and nan values (equivalent to replace([np.inf, -np.inf], 0))
        drivers = drivers.with_columns(
            pl.col("backroom_cases_dotcom")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float32
            )
            .alias("backroom_cases_dotcom")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("backroom_cases_dotcom") > 0)
            .then(pl.col("backroom_cases_dotcom") / pl.col("pallet_capacity"))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("backroom_pallets")
        )
        
        # Handle inf and nan for backroom_pallets
        drivers = drivers.with_columns(
            pl.col("backroom_pallets")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float32
            )
            .alias("backroom_pallets")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("backroom_cases_dotcom") > 0)
            .then(pl.col("cases_delivered_on_sf") - pl.col("backroom_cases_dotcom"))
            .otherwise(pl.col("cases_delivered_on_sf"))
            .cast(pl.Float32)
            .alias("cases_delivered_on_sf")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("cases_delivered_on_sf") < 0)
            .then(pl.lit(0))
            .otherwise(pl.col("cases_delivered_on_sf"))
            .alias("cases_delivered_on_sf")
        )
        
        # Secondary SRP calculation
        drivers = drivers.with_columns(
            pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
            .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("secondary_srp")
        )
        
        # Handle inf and nan for secondary_srp
        drivers = drivers.with_columns(
            pl.col("secondary_srp")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float32
            )
            .alias("secondary_srp")
        )
        
        drivers = drivers.with_columns(
            pl.when((1 - pl.col("shelfCapacity") / pl.col("stock")) > 0.4)
            .then(pl.col("secondary_srp"))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("secondary_srp")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("stock") < pl.col("shelfCapacity"))
            .then(pl.lit(0))
            .otherwise(pl.col("secondary_srp"))
            .alias("secondary_srp")
        )
        
        drivers = drivers.with_columns(
            pl.when(
                (pl.col("srp") > 0) |
                (pl.col("full_pallet") > 0) |
                (pl.col("mu") > 0) |
                (pl.col("split_pallet") > 0) |
                (pl.col("icream_nsrp") > 0)
            )
            .then(pl.col("secondary_srp") / weekdays_to_divide)
            .otherwise(pl.lit(0))
            .alias("secondary_srp")
        )
        
        # Secondary NSRP calculation
        drivers = drivers.with_columns(
            pl.when(pl.col("sold_units") > pl.col("shelfCapacity"))
            .then(pl.col("stock") - (pl.col("shelfCapacity") / (1 - backstock_target)))
            .otherwise(pl.lit(0))
            .cast(pl.Float32)
            .alias("secondary_nsrp")
        )
        
        # Handle inf and nan for secondary_nsrp
        drivers = drivers.with_columns(
            pl.col("secondary_nsrp")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float32
            )
            .alias("secondary_nsrp")
        )
        
        drivers = drivers.with_columns(
            pl.when((1 - (pl.col("shelfCapacity") / pl.col("stock"))) > 0.4)
            .then(pl.col("secondary_nsrp"))
            .otherwise(pl.lit(0))
            .alias("secondary_nsrp")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("stock") < pl.col("shelfCapacity"))
            .then(pl.lit(0))
            .otherwise(pl.col("secondary_nsrp"))
            .alias("secondary_nsrp")
        )
        
        drivers = drivers.with_columns(
            pl.when(
                (pl.col("srp") == 0) &
                (pl.col("full_pallet") == 0) &
                (pl.col("mu") == 0) &
                (pl.col("split_pallet") == 0) &
                (pl.col("icream_nsrp") == 0)
            )
            .then(pl.col("secondary_nsrp") / weekdays_to_divide)
            .otherwise(pl.lit(0))
            .alias("secondary_nsrp")
        )
        
        # Shop floor capacity calculation
        drivers = drivers.with_columns(
            (pl.col("shelfCapacity") + pl.col("secondary_nsrp") + pl.col("secondary_srp"))
            .cast(pl.Float32)
            .alias("shop_floor_capacity")
        )
        
        # Stock morning calculation
        drivers = drivers.with_columns(
            (pl.col("stock") - pl.col("unit") + pl.col("sold_units") + pl.col("sold_units_dotcom"))
            .alias("stock_morning")
        )
        
        # Handle inf and nan for stock_morning
        drivers = drivers.with_columns(
            pl.col("stock_morning")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float64
            )
            .alias("stock_morning")
        )
        
        # Cases to replenish calculation
        drivers = drivers.with_columns([
            pl.min_horizontal([pl.col("stock"), pl.col("shop_floor_capacity")]).alias("stock_shopfloor_min"),
            pl.min_horizontal([pl.col("stock_morning"), pl.col("shop_floor_capacity")]).alias("stockmorning_shopfloor_min")
        ])
        
        drivers = drivers.with_columns(
            (
                (
                    pl.col("sold_units") + 
                    pl.col("stock_shopfloor_min") - 
                    pl.col("stockmorning_shopfloor_min")
                ) / pl.col("case_capacity")
            ).alias("cases_to_replenish")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("cases_to_replenish") < 0)
            .then(pl.lit(0))
            .otherwise(pl.col("cases_to_replenish"))
            .alias("cases_to_replenish")
        )
        
        # ClipStrip Flag
        drivers = drivers.with_columns(
            pl.when(
                pl.col("product_name").cast(pl.Utf8).str.contains("HELL") |
                pl.col("product_name").cast(pl.Utf8).str.contains("XIXO")
            )
            .then(pl.lit(0))
            .otherwise(pl.col("clipstrip_flag"))
            .alias("clipstrip_flag")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("clipstrip_flag") == 1)
            .then(pl.col("cases_to_replenish") * pl.col("case_capacity"))
            .otherwise(pl.lit(0))
            .alias("Clip Strip Items")
        )
        
        drivers = drivers.with_columns(
            (pl.col("Clip Strip Items") / pl.col("case_capacity"))
            .cast(pl.Float32)
            .alias("Clip Strip Cases")
        )
        
        # Handle inf and nan for Clip Strip Cases
        drivers = drivers.with_columns(
            pl.col("Clip Strip Cases")
            .fill_null(0)
            .map_elements(
                lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                return_dtype=pl.Float32
            )
            .alias("Clip Strip Cases")
        )
        
        drivers = drivers.with_columns(
            pl.when(pl.col("clipstrip_flag") == 1)
            .then(pl.lit(0))
            .otherwise(pl.col("cases_to_replenish"))
            .alias("cases_to_replenish")
        )
        
        # Cases to replenish only logic
        if cases_to_replenish_only == True:
            cases_to_replenish_tpn = (
                drivers
                .group_by(['country', 'store', 'division', 'dep', 'pmg', 'tpnb', 'product_name'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
            cases_to_replenish_dep = (
                drivers
                .group_by(['country', 'store', 'division', 'dep'])
                .agg(pl.col('cases_to_replenish').sum())
            )
            
            # Return early if only cases_to_replenish is needed
            return drivers, cases_to_replenish_tpn, cases_to_replenish_dep
        
        if cases_to_replenish_only == False:
            
            # Touch calculations
            drivers = drivers.with_columns(
                pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                .then(pl.col("shop_floor_capacity"))
                .otherwise(pl.col("stock"))
                .cast(pl.Float32)
                .alias("o_touch")
            )
            
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col("is_capping_shelf") == 1) &
                    (
                        pl.col("stock") - (capping_shelves_ratio * pl.col("shop_floor_capacity"))
                        > pl.col("shop_floor_capacity")
                    )
                )
                .then(capping_shelves_ratio * pl.col("shop_floor_capacity"))
                .otherwise(pl.lit(0))
                .alias("c_touch")
            )
            
            drivers = drivers.with_columns(
                pl.when(pl.col("stock") > pl.col("shop_floor_capacity"))
                .then(pl.col("stock") - pl.col("c_touch") - pl.col("shop_floor_capacity"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("t_touch")
            )
            
            # Capping Shelf Cases
            drivers = drivers.with_columns(
                ((pl.col("c_touch") / pl.col("case_capacity")) / weekdays_to_divide)
                .cast(pl.Float32)
                .alias("Capping Shelf Cases")
            )
            
            # Handle inf and nan for Capping Shelf Cases
            drivers = drivers.with_columns(
                pl.col("Capping Shelf Cases")
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Capping Shelf Cases")
            )
            
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(pl.col("Capping Shelf Cases"))
                .alias("Capping Shelf Cases")
            )
            
            # Cases to replenish excluding cap cases
            drivers = drivers.with_columns(
                pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                .then(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases"))
                .otherwise(pl.col("cases_to_replenish"))
                .alias("cases_to_repl_excl_cap_cases")
            )
            
            drivers = drivers.with_columns(
                pl.when(pl.col("cases_to_replenish") - pl.col("Capping Shelf Cases") > 0)
                .then(pl.col("Capping Shelf Cases"))
                .otherwise(pl.lit(0))
                .alias("Capping Shelf Cases")
            )
            
            # Prepare store inputs dataframes
            if isinstance(store_inputs, pd.DataFrame):
                store_inputs_pl = pl.from_pandas(store_inputs)
            else:
                store_inputs_pl = store_inputs
            
            # Prepare store inputs dataframes
            stores_df = (
                store_inputs_pl
                .select(["Store", "Format", "Plan Size"])
                .unique()
                .with_columns([
                    pl.col("Store").cast(pl.Int64()).alias("store"),
                    pl.col("Format").cast(pl.Utf8).alias("format"),
                    pl.col("Plan Size").cast(pl.Utf8).alias("plan size")
                ])
                .drop(["Store", "Format", "Plan Size"])
            )
            
            dep_df = (
                store_inputs_pl
                .select([
                    "Store", "Dep", "Racking", "Pallets Delivery Ratio",
                    "Backstock Pallet Ratio", "says", "pbl_pbs_25_perc_pallet"
                ])
                .unique()
                .with_columns([
                    pl.col("Store").cast(pl.Int64()).alias("store"),
                    pl.col("Dep").cast(pl.Utf8).alias("dep"),
                    pl.col("Racking").cast(pl.Utf8).alias("racking"),
                    pl.col("Pallets Delivery Ratio").cast(pl.Utf8).alias("pallets delivery ratio"),
                    pl.col("Backstock Pallet Ratio").cast(pl.Utf8).alias("backstock pallet ratio"),
                    pl.col("says").cast(pl.Utf8).alias("says"),
                    pl.col("pbl_pbs_25_perc_pallet").cast(pl.Utf8).alias("pbl_pbs_25_perc_pallet")
                ])
                .drop(["Store", "Dep", "Racking", "Pallets Delivery Ratio", 
                       "Backstock Pallet Ratio", "says", "pbl_pbs_25_perc_pallet"])
            )
            
            pmg_df = (
                store_inputs_pl
                .select(["Country", "Format", "Pmg", "presortPerc", "prack"])
                .unique()
                .with_columns([
                    pl.col("Country").cast(pl.Utf8).alias("country"),
                    pl.col("Format").cast(pl.Utf8).alias("format"),
                    pl.col("Pmg").cast(pl.Utf8).alias("pmg"),
                    pl.col("presortPerc").cast(pl.Utf8).alias("presortperc"),
                    pl.col("prack").cast(pl.Utf8).alias("prack")
                ])
                .drop(["Country", "Format", "Pmg", "presortPerc", "prack"])
            )
            
            # Format standardization and merging
            drivers = drivers.with_columns(
                pl.when(pl.col("format") == "1k")
                .then(pl.lit("1K"))
                .otherwise(pl.col("format"))
                .alias("format")
            )
            
            
            
            
            drivers = drivers.with_columns([
                                        pl.col("format").cast(pl.Utf8), 
                                        pl.col("country").cast(pl.Utf8),
                                        pl.col("dep").cast(pl.Utf8),
                                        pl.col("pmg").cast(pl.Utf8)
                                    ])
            
            # Merge dataframes
            drivers = drivers.join(stores_df, on=["store", "format"], how="inner")
            drivers = drivers.join(pmg_df, on=["country", "format", "pmg"], how="left")
            drivers = drivers.join(dep_df, on=["store", "dep"], how="left")
            
            drivers = drivers.with_columns(
                pl.when(pl.col("racking") == 1)
                .then(pl.col("prack"))
                .otherwise(pl.lit(0))
                .alias("prack")
            )
            
            
            # Try to update presortperc if pre_sort_perc_by_pmg exists
            try:
                drivers = drivers.with_columns(
                    pl.when(pl.col("dep").is_in(['BWS', 'HEA', 'DRY']))
                    .then(pl.col('pre_sort_perc_by_pmg'))
                    .otherwise(pl.col('presortperc'))
                    .alias('presortperc')
                )
            except:
                pass
            
            # New Delivery - Rollcages
            drivers = drivers.with_columns(
                (
                    (pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")) *
                    RC_Capacity_Ratio *
                    (1 - pl.col("pallets delivery ratio"))
                )
                .cast(pl.Float32)
                .alias("New Delivery - Rollcages")
            )
            
            # Handle inf and nan for New Delivery - Rollcages
            drivers = drivers.with_columns(
                pl.col("New Delivery - Rollcages")
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("New Delivery - Rollcages")
            )
            
            # Try PBL/PBS calculations
            try:
                # Combine MIX and PBL columns
                drivers = drivers.with_columns([
                    (pl.col('MIX_CAGE_%') + pl.col('PBL_CAGE_%')).alias('PBL_CAGE_%'),
                    (pl.col('MIX_PALLET_%') + pl.col('PBL_PALLET_%')).alias('PBL_PALLET_%')
                ])
                
                # Handle zero sum rows for cage and pallet columns
                cage_columns = ['PBL_CAGE_%', 'PBS_CAGE_%']
                pallet_columns = ['PBL_PALLET_%', 'PBS_PALLET_%']
                
                for cols in [cage_columns, pallet_columns]:
                    drivers = drivers.with_columns([
                        pl.when((pl.col(cols[0]) + pl.col(cols[1])) == 0)
                        .then(pl.lit(0.5))
                        .otherwise(pl.col(cols[0]))
                        .alias(cols[0]),
                        
                        pl.when((pl.col(cols[0]) + pl.col(cols[1])) == 0)
                        .then(pl.lit(0.5))
                        .otherwise(pl.col(cols[1]))
                        .alias(cols[1])
                    ])
                
                # Create new columns for cage calculations
                for col in ['PBL_CAGE_%', 'PBS_CAGE_%']:
                    new_col = col[:-2]  # Remove the '%' at the end
                    drivers = drivers.with_columns(
                        (pl.col(col) * pl.col('New Delivery - Rollcages'))
                        .cast(pl.Float32)
                        .alias(new_col)
                    )
            except:
                pass
            
            # New Delivery - Pallets
            drivers = drivers.with_columns(
                (
                    (pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")) *
                    pl.col("pallets delivery ratio")
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("New Delivery - Pallets")
            )
            
            # Try pallet calculations
            try:
                # Create new columns for pallet calculations
                for col in ['PBL_PALLET_%', 'PBS_PALLET_%']:
                    new_col = col[:-2]  # Remove the '%' at the end
                    drivers = drivers.with_columns(
                        (pl.col(col) * pl.col("New Delivery - Pallets"))
                        .cast(pl.Float32)
                        .alias(new_col)
                    )
                
                # Handle null values in pallet percentage columns
                for col in ['PBL_PALLET_%', 'PBS_PALLET_%']:
                    drivers = drivers.with_columns(
                        pl.col(col)
                        .fill_null(0.5)
                        .cast(pl.Float32)
                        .alias(col)
                    )
            except:
                pass
            
            # New Delivery - Shelf Trolley
            drivers = drivers.with_columns(
                (
                    (pl.col("cases_delivered_on_sf") / pl.col("pallet_capacity")) *
                    shelf_trolley_cap_ratio_to_pallet
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("New Delivery - Shelf Trolley")
            )
            
            # Pre-sorted Cases calculations
            drivers = drivers.with_columns(
                (
                    pl.col("presortperc") *
                    pl.col("cases_delivered_on_sf") *
                    pl.col("pallets delivery ratio")
                )
                .alias("Pre-sorted Cases")
            )
            
            # Pre-sorted Cases for PBL/PBS (same calculation for stock movement perspective)
            drivers = drivers.with_columns(
                (
                    pl.col("presortperc") *
                    pl.col("cases_delivered_on_sf") *
                    pl.col("pallets delivery ratio")
                )
                .alias("Pre-sorted Cases_pbl_pbs")
            )
            
            # Light and Heavy Pre-sorted Cases
            drivers = drivers.with_columns([
                pl.when(pl.col("light") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("L_Pre-sorted Cases"),
                
                pl.when(pl.col("heavy") == 1)
                .then(pl.col("Pre-sorted Cases"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("H_Pre-sorted Cases")
            ])
            
            # Handle inf and nan for L_Pre-sorted and H_Pre-sorted Cases
            for col in ["L_Pre-sorted Cases", "H_Pre-sorted Cases"]:
                drivers = drivers.with_columns(
                    pl.col(col)
                    .fill_null(0)
                    .map_elements(
                        lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                        return_dtype=pl.Float32
                    )
                    .alias(col)
                )
            
            # Full Pallet and MU cases
            drivers = drivers.with_columns([
                (
                    pl.col("cases_to_replenish") *
                    pl.when(pl.col("full_pallet") > 0)
                    .then(pl.col("full_pallet"))
                    .otherwise(pl.lit(0))
                )
                .cast(pl.Float32)
                .alias("Full Pallet Cases"),
                
                (
                    pl.col("cases_to_replenish") *
                    pl.when(pl.col("mu") > 0)
                    .then(pl.col("mu"))
                    .otherwise(pl.lit(0))
                )
                .cast(pl.Float32)
                .alias("MU cases")
            ])
            
            # Full + Half Pallet Cases
            drivers = drivers.with_columns(
                (pl.col("Full Pallet Cases") + pl.col("MU cases"))
                .alias("Full + Half Pallet Cases")
            )
            
            # Racking Pallets
            drivers = drivers.with_columns(
                (pl.col("prack") * pl.col("New Delivery - Pallets"))
                .alias("Racking Pallets")
            )
            
            # Replenished Rollcages calculation
            drivers = drivers.with_columns(
                (
                    pl.col("New Delivery - Rollcages") +
                    (
                        pl.col("Pre-sorted Cases_pbl_pbs") /
                        pl.col("pallet_capacity") *
                        pl.col("pallets delivery ratio")
                    ) * RC_Capacity_Ratio
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Replenished Rollcages")
            )
            
            # Try PBS/PBL rollcage calculations
            try:
                drivers = drivers.with_columns([
                    (
                        (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                         pl.col("pallets delivery ratio")) * RC_Capacity_Ratio
                    ).alias('pre_sort_rc'),
                    
                    (
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                          pl.col("pallets delivery ratio")) * RC_Capacity_Ratio) *
                        pl.col('PBS_CAGE_%')
                    ).alias('pre_sort_rc_pbs'),
                    
                    (
                        ((pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                          pl.col("pallets delivery ratio")) * RC_Capacity_Ratio) *
                        pl.col('PBL_CAGE_%')
                    ).alias('pre_sort_rc_pbl')
                ])
                
                drivers = drivers.with_columns([
                    (pl.col("PBS_CAGE") + pl.col('pre_sort_rc_pbs')).alias("Replenished Rollcages PBS"),
                    (pl.col("PBL_CAGE") + pl.col('pre_sort_rc_pbl')).alias("Replenished Rollcages PBL")
                ])
            except:
                pass
            
            # Replenished Pallets calculation
            pre_sort_pallet_calc = (
                pl.col("Pre-sorted Cases_pbl_pbs") /
                pl.col("pallet_capacity") *
                pl.col("pallets delivery ratio")
            )
            
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col("New Delivery - Pallets") - pre_sort_pallet_calc) <= 0
                )
                .then(pl.lit(0))
                .otherwise(pl.col("New Delivery - Pallets") - pre_sort_pallet_calc)
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Replenished Pallets")
            )
            
            # Try PBS/PBL pallet calculations
            try:
                drivers = drivers.with_columns([
                    (
                        pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                        pl.col("pallets delivery ratio")
                    ).alias('pre_sort_pal'),
                    
                    (
                        (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                         pl.col("pallets delivery ratio")) * pl.col('PBS_PALLET_%')
                    ).alias('pre_sort_pal_pbs'),
                    
                    (
                        (pl.col("Pre-sorted Cases_pbl_pbs") / pl.col("pallet_capacity") *
                         pl.col("pallets delivery ratio")) * pl.col('PBL_PALLET_%')
                    ).alias('pre_sort_pal_pbl')
                ])
                
                drivers = drivers.with_columns([
                    pl.when((pl.col("PBS_PALLET") - pl.col('pre_sort_pal_pbs')) <= 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("PBS_PALLET") - pl.col('pre_sort_pal_pbs'))
                    .alias("Replenished Pallets PBS"),
                    
                    pl.when((pl.col("PBL_PALLET") - pl.col('pre_sort_pal_pbl')) <= 0)
                    .then(pl.lit(0))
                    .otherwise(pl.col("PBL_PALLET") - pl.col('pre_sort_pal_pbl'))
                    .alias("Replenished Pallets PBL")
                ])
            except:
                pass
            
            # Replenished Shelf Trolley
            drivers = drivers.with_columns(
                (
                    (pl.col("Replenished Rollcages") * shelf_trolley_cap_ratio_to_rollcage) +
                    (pl.col("Replenished Pallets") * shelf_trolley_cap_ratio_to_pallet)
                )
                .cast(pl.Float32)
                .alias("Replenished Shelf Trolley")
            )
            
            # Zero out replenished columns for full_pallet and mu cases
            replenish_cols = [
                "Replenished Rollcages", "Replenished Pallets", 
                "Replenished Pallets PBL", "Replenished Pallets PBS",
                "Replenished Rollcages PBL", "Replenished Rollcages PBS"
            ]
            
            for col in replenish_cols:
                try:
                    drivers = drivers.with_columns(
                        pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                        .then(pl.lit(0))
                        .otherwise(pl.col(col))
                        .alias(col)
                    )
                except:
                    pass
            
            # Unit for tagging
            drivers = drivers.with_columns(
                (
                    (pl.col("cases_to_repl_excl_cap_cases") + pl.col("Capping Shelf Cases")) *
                    pl.col("case_capacity")
                )
                .alias("Unit_for_tagging")
            )
            
            drivers = drivers.with_columns(
                pl.when(pl.col("Unit_for_tagging") < 0)
                .then(pl.lit(0))
                .otherwise(pl.col("Unit_for_tagging"))
                .alias("Unit_for_tagging")
            )
            
            # UK BackStock Logic
            drivers = drivers.with_columns([
                # looseSingles
                pl.when(pl.col('stock') > pl.col('case_capacity'))
                .then(pl.col('stock') % pl.col('case_capacity'))
                .otherwise(pl.lit(0))
                .alias('looseSingles'),
                
                # inCaseSingles
                (pl.col('stock') - pl.col('looseSingles')).alias('inCaseSingles')
            ])
            
            # Note: We need to compute looseSingles first, then use it for inCaseSingles
            drivers = drivers.with_columns(
                (pl.col('stock') - pl.col('looseSingles')).alias('inCaseSingles')
            )
            
            # spaceOnShelfSingles
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col('shelfCapacity') > pl.col('looseSingles')) &
                    (pl.col('stock') > pl.col('shelfCapacity'))
                )
                .then(pl.col('shelfCapacity') - pl.col('looseSingles'))
                .otherwise(pl.col('shelfCapacity') - pl.col('stock') - pl.col('looseSingles'))
                .alias('spaceOnShelfSingles')
            )
            
            drivers = drivers.with_columns(
                pl.when(pl.col('spaceOnShelfSingles') < 0)
                .then(pl.lit(0))
                .otherwise(pl.col('spaceOnShelfSingles'))
                .alias('spaceOnShelfSingles')
            )
            
            # caseUnitSpaceOnShelf
            drivers = drivers.with_columns(
                (pl.col('spaceOnShelfSingles') - (pl.col('spaceOnShelfSingles') % pl.col('case_capacity')))
                .alias('caseUnitSpaceOnShelf')
            )
            
            # fullCaseSingleShelf
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col('caseUnitSpaceOnShelf') >= pl.col('inCaseSingles')) &
                    (pl.col('stock') > pl.col('caseUnitSpaceOnShelf'))
                )
                .then(pl.col('caseUnitSpaceOnShelf'))
                .otherwise(pl.col('stock') - pl.col('looseSingles'))
                .alias('fullCaseSingleShelf')
            )
            
            # totalSinglesShelf
            drivers = drivers.with_columns(
                pl.when((pl.col('fullCaseSingleShelf') + pl.col('looseSingles')) > pl.col('shelfCapacity'))
                .then(pl.col('shelfCapacity'))
                .otherwise(pl.col('fullCaseSingleShelf') + pl.col('looseSingles'))
                .alias('totalSinglesShelf')
            )
            
            # totalSinglesBackstock
            drivers = drivers.with_columns(
                pl.when(pl.col('stock') > pl.col('totalSinglesShelf'))
                .then(pl.col('stock') - pl.col('totalSinglesShelf'))
                .otherwise(pl.lit(0))
                .alias('totalSinglesBackstock')
            )
            
            # totalCasesShelf and Backstock Cases
            drivers = drivers.with_columns([
                (pl.col('totalSinglesShelf') / pl.col('case_capacity')).alias('totalCasesShelf'),
                ((pl.col('totalSinglesBackstock') / pl.col('case_capacity')) / weekdays_to_divide).alias('Backstock Cases'),
                (pl.col('totalSinglesBackstock') / weekdays_to_divide).alias('Backstock unit')
            ])
            
            # Post-sort logic
            drivers = drivers.with_columns(
                (pl.col("t_touch") / pl.col("stock"))
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float64
                )
                .alias("post_sort_ratio")
            )
            
            # Update Backstock Cases for GM division
            drivers = drivers.with_columns(
                pl.when(pl.col("division") == 'GM')
                .then(pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
                .otherwise(pl.col('Backstock Cases'))
                .alias('Backstock Cases')
            )
            
            # Adjust Backstock Cases by subtracting Capping Shelf Cases
            drivers = drivers.with_columns(
                pl.when(pl.col('Backstock Cases') - pl.col('Capping Shelf Cases') > 0)
                .then(pl.col('Backstock Cases') - pl.col('Capping Shelf Cases'))
                .otherwise(pl.lit(0))
                .alias('Backstock Cases')
            )
            
            # Backstock_tpn_nr calculation using window function
            drivers = drivers.with_columns(
                pl.when(
                    pl.col('Backstock Cases').sum().over(['store', 'tpnb']) > 0
                )
                .then(pl.lit(1/7))
                .otherwise(pl.lit(0))
                .alias('Backstock_tpn_nr')
            )
            
            # Post-sort Cases
            drivers = drivers.with_columns(
                (pl.col("cases_to_replenish") * pl.col("post_sort_ratio"))
                .alias("Post-sort Cases")
            )
            
            # Two Touch Cases and unit
            drivers = drivers.with_columns([
                ((pl.col("t_touch") / pl.col("case_capacity")) / weekdays_to_divide)
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Two Touch Cases"),
                
                (pl.col("t_touch") / weekdays_to_divide).alias("Two Touch unit")
            ])
            
            # Post-sort Pallets
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.col("Post-sort Cases") / pl.col("pallet_capacity"))
                .otherwise(
                    pl.col("Post-sort Cases") / pl.col("pallet_capacity") *
                    pl.col("backstock pallet ratio")
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Post-sort Pallets")
            )
            
            # Update Post-sort Cases (zero out for full_pallet and mu)
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(pl.col("Post-sort Cases"))
                .alias("Post-sort Cases")
            )
            
            # Light and Heavy Post-sort Cases
            drivers = drivers.with_columns([
                pl.when(pl.col("light") == 1)
                .then(pl.col("Post-sort Cases"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("L_Post-sort Cases"),
                
                pl.when(pl.col("heavy") == 1)
                .then(pl.col("Post-sort Cases"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("H_Post-sort Cases")
            ])
            
            # Post-sort Rollcages
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(
                    (
                        pl.col("Post-sort Cases") /
                        pl.col("pallet_capacity") *
                        (1 - pl.col("backstock pallet ratio")) *
                        RC_Capacity_Ratio
                    )
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Post-sort Rollcages")
            )
            
            # Backstock Pallets
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.col("Backstock Cases") / pl.col("pallet_capacity"))
                .otherwise(
                    pl.col("Backstock Cases") / pl.col("pallet_capacity") *
                    pl.col("backstock pallet ratio")
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Backstock Pallets")
            )
            
            # Update Backstock Cases and unit (zero out for full_pallet and mu)
            drivers = drivers.with_columns([
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(pl.col("Backstock Cases"))
                .alias("Backstock Cases"),
                
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(pl.col("Backstock unit"))
                .alias("Backstock unit")
            ])
            
            # Backstock Rollcages
            drivers = drivers.with_columns(
                pl.when((pl.col("full_pallet") > 0) | (pl.col("mu") > 0))
                .then(pl.lit(0))
                .otherwise(
                    (
                        pl.col("Backstock Cases") /
                        pl.col("pallet_capacity") *
                        (1 - pl.col("backstock pallet ratio")) *
                        RC_Capacity_Ratio
                    )
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Backstock Rollcages")
            )
            
            # Backstock Shelf Trolley
            drivers = drivers.with_columns(
                (
                    (pl.col("Backstock Rollcages") * shelf_trolley_cap_ratio_to_rollcage) +
                    (pl.col("Backstock Pallets") * shelf_trolley_cap_ratio_to_pallet)
                )
                .alias("Backstock Shelf Trolley")
            )
            
            # Pre-sorted Rollcages
            drivers = drivers.with_columns(
                ((pl.col("Pre-sorted Cases") / pl.col("pallet_capacity")) * RC_Capacity_Ratio)
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Pre-sorted Rollcages")
            )
            
            # Pre-sorted Shelf Trolley
            drivers = drivers.with_columns(
                (
                    (pl.col("Pre-sorted Cases") / pl.col("pallet_capacity")) *
                    shelf_trolley_cap_ratio_to_pallet
                )
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float64
                )
                .alias("Pre-sorted Shelf Trolley")
            )
            
            # Full Pallet and MU Pallet calculations
            drivers = drivers.with_columns([
                (pl.col("Full Pallet Cases") / pl.col("pallet_capacity"))
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("Full Pallet"),
                
                (pl.col("MU cases") / pl.col("pallet_capacity"))
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("MU Pallet")
            ])
            
            # One Touch Cases
            drivers = drivers.with_columns(
                ((pl.col("o_touch") / pl.col("case_capacity")) / weekdays_to_divide)
                .fill_null(0)
                .map_elements(
                    lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                    return_dtype=pl.Float32
                )
                .alias("One Touch Cases")
            )
            
            # Country-specific foil adjustment
            if tpnb_country:
                drivers = drivers.with_columns(pl.lit(1).alias("foil"))
            
            # SRP split pallet calculation
            drivers = drivers.with_columns(
                (pl.col("srp") + pl.col("split_pallet")).alias("srp_split_pallet")
            )
            
            # L_SRP calculation
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1)
                )
                .then(
                    pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet")
                )
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("L_SRP")
            )
            
            # Apply SRP opening reduction opportunity for L_SRP
            drivers = drivers.with_columns(
                pl.when(pl.col('SRP opening reduction opportunity') == 1)
                .then(pl.col("L_SRP") * pl.col("foil"))
                .otherwise(pl.col("L_SRP"))
                .alias("L_SRP")
            )
            
            # H_SRP calculation
            drivers = drivers.with_columns(
                pl.when(
                    (pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1)
                )
                .then(
                    pl.col("cases_to_repl_excl_cap_cases") * pl.col("srp_split_pallet")
                )
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("H_SRP")
            )
            
            # Apply SRP opening reduction opportunity for H_SRP
            drivers = drivers.with_columns(
                pl.when(pl.col('SRP opening reduction opportunity') == 1)
                .then(pl.col("H_SRP") * pl.col("foil"))
                .otherwise(pl.col("H_SRP"))
                .alias("H_SRP")
            )
            
            # L_NSRP and H_NSRP calculations
            drivers = drivers.with_columns([
                pl.when(
                    (pl.col("nsrp") > 0) & (pl.col("light") == 1)
                )
                .then(
                    (pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp")) * pl.col("foil")
                )
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("L_NSRP"),
                
                pl.when(
                    (pl.col("nsrp") > 0) & (pl.col("heavy") == 1)
                )
                .then(
                    (pl.col("cases_to_repl_excl_cap_cases") * pl.col("nsrp")) * pl.col("foil")
                )
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("H_NSRP")
            ])
            
            # Foil Cases calculation (sum across multiple columns)
            drivers = drivers.with_columns(
                pl.when(pl.col("foil") != 1)
                .then(
                    (1 - pl.col("foil")) *
                    pl.col("cases_to_repl_excl_cap_cases") *
                    (pl.col("srp") + pl.col("nsrp") + pl.col("icream_nsrp"))
                )
                .otherwise(pl.lit(0))
                .alias("Foil_Cases")
            )
            
            # Country-specific secondary calculations
            if tpnb_country == False:
                drivers = drivers.with_columns([
                    (pl.col("secondary_srp") / pl.col("case_capacity"))
                    .fill_null(0)
                    .map_elements(
                        lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                        return_dtype=pl.Float32
                    )
                    .alias("Sec_SRP_cases"),
                    
                    (pl.col("secondary_nsrp") / pl.col("case_capacity"))
                    .fill_null(0)
                    .map_elements(
                        lambda x: 0 if x == float('inf') or x == float('-inf') else x,
                        return_dtype=pl.Float32
                    )
                    .alias("Sec_NSRP_cases")
                ])
            
            if tpnb_country:
                drivers = drivers.with_columns([
                    pl.lit(0).alias("Sec_SRP_cases"),
                    pl.lit(0).alias("Sec_NSRP_cases")
                ])
            
            # NSRP Items calculations
            drivers = drivers.with_columns([
                (pl.col("L_NSRP") * pl.col("case_capacity")).alias("L_NSRP_Items"),
                (pl.col("H_NSRP") * pl.col("case_capacity")).alias("H_NSRP_Items")
            ])
            
            # Hook Fill calculations for specific PMGs
            hook_pmgs = ["DRY13", "HDL21", "PPD02"]
            
            drivers = drivers.with_columns([
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.col("L_NSRP"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("L_Hook Fill Cases"),
                
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.col("H_NSRP"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("H_Hook Fill Cases"),
                
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.col("L_NSRP_Items") + pl.col("H_NSRP_Items"))
                .otherwise(pl.lit(0))
                .cast(pl.Float32)
                .alias("Hook Fill Items")
            ])
            
            # Zero out NSRP columns for hook PMGs
            drivers = drivers.with_columns([
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.lit(0))
                .otherwise(pl.col("L_NSRP"))
                .cast(pl.Float32)
                .alias("L_NSRP"),
                
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.lit(0))
                .otherwise(pl.col("H_NSRP"))
                .cast(pl.Float32)
                .alias("H_NSRP"),
                
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.lit(0))
                .otherwise(pl.col("L_NSRP_Items"))
                .cast(pl.Float32)
                .alias("L_NSRP_Items"),
                
                pl.when(pl.col("pmg").is_in(hook_pmgs))
                .then(pl.lit(0))
                .otherwise(pl.col("H_NSRP_Items"))
                .cast(pl.Float32)
                .alias("H_NSRP_Items")
            ])
            
            # Add secondary cases to SRP and NSRP
            drivers = drivers.with_columns([
                pl.when(
                    (pl.col("srp_split_pallet") > 0) & (pl.col("light") == 1)
                )
                .then(pl.col("L_SRP") + pl.col("Sec_SRP_cases"))
                .otherwise(pl.col("L_SRP"))
                .alias("L_SRP"),
                
                pl.when(
                    (pl.col("srp_split_pallet") > 0) & (pl.col("heavy") == 1)
                )
                .then(pl.col("H_SRP") + pl.col("Sec_SRP_cases"))
                .otherwise(pl.col("H_SRP"))
                .alias("H_SRP"),
                
                pl.when(
                    (pl.col("nsrp") > 0) & (pl.col("light") == 1)
                )
                .then(pl.col("L_NSRP") + pl.col("Sec_NSRP_cases"))
                .otherwise(pl.col("L_NSRP"))
                .alias("L_NSRP"),
                
                pl.when(
                    (pl.col("nsrp") > 0) & (pl.col("heavy") == 1)
                )
                .then(pl.col("H_NSRP") + pl.col("Sec_NSRP_cases"))
                .otherwise(pl.col("H_NSRP"))
                .alias("H_NSRP")
            ])
            
            # Heavy case NSRP items based on weight
            drivers = drivers.with_columns([
                pl.when(pl.col("weight") <= 1.5)
                .then(pl.col("H_NSRP") * pl.col("case_capacity"))
                .otherwise(pl.lit(0))
                .alias("H_CASE_L_NSRP_items"),
                
                pl.when(pl.col("weight") > 1.5)
                .then(pl.col("H_NSRP") * pl.col("case_capacity"))
                .otherwise(pl.lit(0))
                .alias("H_CASE_H_NSRP_items")
            ])
            
            # High pallet calculations for DRY30 and DRY24
            high_pmgs = ["DRY30", "DRY24"]
            
            drivers = drivers.with_columns([
                # High pallet cases
                pl.when(
                    (pl.col("full_pallet") > 0) & pl.col("pmg").is_in(high_pmgs)
                )
                .then(pl.col("Full Pallet Cases") * 0.2)
                .otherwise(pl.lit(0))
                .alias("High_pallet_cases_on_Dry30_and_DRY24"),
                
                # High pallets
                pl.when(
                    (pl.col("full_pallet") > 0) & pl.col("pmg").is_in(high_pmgs)
                )
                .then(pl.col("Full Pallet"))
                .otherwise(pl.lit(0))
                .alias("High_pallets_on_Dry30_and_DRY24"),
                
                # High half pallet cases
                pl.when(
                    (pl.col("mu") > 0) & pl.col("pmg").is_in(high_pmgs)
                )
                .then(pl.col("MU cases") * 0.2)
                .otherwise(pl.lit(0))
                .alias("High_half_pallet_cases_on_Dry30_and_DRY24"),
                
                # High half pallets
                pl.when(
                    (pl.col("mu") > 0) & pl.col("pmg").is_in(high_pmgs)
                )
                .then(pl.col("MU Pallet"))
                .otherwise(pl.lit(0))
                .alias("High_half_pallets_on_Dry30_and_DRY24")
            ])

            drivers = drivers.to_pandas()

                        #tagging logic
            drivers = tagging_on_product(drivers)
            # check_tag_values(drivers)

    
            # drivers = tagging_old_model(drivers)
            
            
    
            drivers["Bulk Pallets"] = drivers["Full Pallet"] + drivers["MU Pallet"]
            
            
            
            drivers["Total RC's and Pallets"] = (
                drivers["Replenished Rollcages"]
                + drivers["Replenished Pallets"]
                + drivers["Backstock Rollcages"]
                + drivers["Backstock Pallets"]
                + drivers["Bulk Pallets"]
            )
    
    
            
            
            
            
    
            
            tray_hood_cond = [
                (drivers["opening_type"] == "Tray + Hood")
                & (drivers.light == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray + Hood") & (drivers.light == 1) & (drivers.nsrp > 0),
                (drivers["opening_type"] == "Tray + Hood")
                & (drivers.heavy == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray + Hood") & (drivers.heavy == 1) & (drivers.nsrp > 0),
                #(drivers["opening_type"] == "Tray + Hood") & (drivers.icream_nsrp > 0),
            ]
            perf_box_cond = [
                (drivers["opening_type"] == "Perforated box")
                & (drivers.light == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Perforated box")
                & (drivers.light == 1)
                & (drivers.nsrp > 0),
                (drivers["opening_type"] == "Perforated box")
                & (drivers.heavy == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Perforated box")
                & (drivers.heavy == 1)
                & (drivers.nsrp > 0),
            ]
            shrink_cond = [
                (drivers["opening_type"] == "Shrink")
                & (drivers.light == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Shrink") & (drivers.light == 1) & (drivers.nsrp > 0),
                (drivers["opening_type"] == "Shrink")
                & (drivers.heavy == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Shrink") & (drivers.heavy == 1) & (drivers.nsrp > 0),
            ]
            tray_shrink_cond = [
                (drivers["opening_type"] == "Tray + Shrink")
                & (drivers.light == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray + Shrink") & (drivers.light == 1) & (drivers.nsrp > 0),
                (drivers["opening_type"] == "Tray + Shrink")
                & (drivers.heavy == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray + Shrink") & (drivers.heavy == 1) & (drivers.nsrp > 0),
            ]
            tray_cond = [
                (drivers["opening_type"] == "Tray")
                & (drivers.light == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray") & (drivers.light == 1) & (drivers.nsrp > 0),
                (drivers["opening_type"] == "Tray")
                & (drivers.heavy == 1)
                & (drivers.srp_split_pallet > 0),
                (drivers["opening_type"] == "Tray") & (drivers.heavy == 1) & (drivers.nsrp > 0),
            ]
            results = [
                drivers.L_SRP + drivers["Capping Shelf Cases"],
                drivers.L_NSRP + drivers["Capping Shelf Cases"],
                drivers.H_SRP + drivers["Capping Shelf Cases"],
                drivers.H_NSRP + drivers["Capping Shelf Cases"],
            ]
    
            drivers["Ownbrand_tray_with_hood_cases"] = np.select(
                tray_hood_cond, results, 0
            ).astype("float32")
            drivers["Ownbrand_perforated_box_cases"] = np.select(
                perf_box_cond, results, 0
            ).astype("float32")
            drivers["Ownbrand_shrink_cases"] = np.select(shrink_cond, results, 0).astype(
                "float32"
            )
            drivers["Ownbrand_tray_with_shrink_cases"] = np.select(
                tray_shrink_cond, results, 0
            ).astype("float32")
            drivers["Ownbrand_tray_cases"] = np.select(tray_cond, results, 0).astype(
                "float32"
            )
            drivers["total_ownbrand_op_type"] = np.where(drivers["opening_type"] == 'no_data', 0, 1)
            drivers["total_ownbrand_op_cases"] = (
                drivers["Ownbrand_tray_with_hood_cases"]
                + drivers["Ownbrand_perforated_box_cases"]
                + drivers["Ownbrand_shrink_cases"]
                + drivers["Ownbrand_tray_with_shrink_cases"]
                + drivers["Ownbrand_tray_cases"]
            )
    
    
            
            # split_pallet opening cases customization
            L_split_p_cond = [
                (drivers.pmg.isin(["DRY24", "BWS01"]))
                & (drivers.split_pallet > 0)
                & (drivers.light == 1),
                (drivers.division.isin(["Grocery"]))
                & (~drivers.pmg.isin(["DRY24", "BWS01"]))
                & (drivers.split_pallet > 0)
                & (drivers.light == 1),
                (drivers.division.isin(["GM"]))
                & (drivers.split_pallet > 0)
                & (drivers.light == 1),
            ]
            L_split_p_res = [drivers.L_SRP * 0, drivers.L_SRP * 0.25, drivers.L_SRP * 1]
            split_p_res_a = [drivers.Ownbrand_tray_with_hood_cases * 0, drivers.Ownbrand_tray_with_hood_cases * 0.25, drivers.Ownbrand_tray_with_hood_cases * 1]
            split_p_res_b = [drivers.Ownbrand_perforated_box_cases * 0, drivers.Ownbrand_perforated_box_cases * 0.25, drivers.Ownbrand_perforated_box_cases * 1]
            split_p_res_c = [drivers.Ownbrand_shrink_cases * 0, drivers.Ownbrand_shrink_cases * 0.25, drivers.Ownbrand_shrink_cases * 1]
            split_p_res_d = [drivers.Ownbrand_tray_with_shrink_cases * 0, drivers.Ownbrand_tray_with_shrink_cases * 0.25, drivers.Ownbrand_tray_with_shrink_cases * 1]
            split_p_res_e = [drivers.Ownbrand_tray_cases * 0, drivers.Ownbrand_tray_cases * 0.25, drivers.Ownbrand_tray_cases * 1]
            
            for x , y in zip(["Ownbrand_tray_with_hood_cases", "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases", "Ownbrand_tray_with_shrink_cases", "Ownbrand_tray_cases"], [split_p_res_a, split_p_res_b, split_p_res_c, split_p_res_d, split_p_res_e]):
                
                drivers[x] = np.select(
                    L_split_p_cond, y, drivers[x]
                )
            
            drivers["L_split_pallet_cases_for_opening"] = np.select(
                L_split_p_cond, L_split_p_res, 0
            )
    
            H_split_p_cond = [
                (drivers.pmg.isin(["DRY24", "BWS01"]))
                & (drivers.split_pallet > 0)
                & (drivers.heavy == 1),
                (drivers.division.isin(["Grocery"]))
                & (~drivers.pmg.isin(["DRY24", "BWS01"]))
                & (drivers.split_pallet > 0)
                & (drivers.heavy == 1),
                (drivers.division.isin(["GM"]))
                & (drivers.split_pallet > 0)
                & (drivers.heavy == 1),
            ]
            H_split_p_res = [drivers.H_SRP * 0, drivers.H_SRP * 0.25, drivers.H_SRP * 1]
            
            
            for x , y in zip(["Ownbrand_tray_with_hood_cases", "Ownbrand_perforated_box_cases", "Ownbrand_shrink_cases", "Ownbrand_tray_with_shrink_cases", "Ownbrand_tray_cases"], [split_p_res_a, split_p_res_b, split_p_res_c, split_p_res_d, split_p_res_e]):
                
                drivers[x] = np.select(
                    H_split_p_cond, y, drivers[x]
                )
            
            
            
            drivers["H_split_pallet_cases_for_opening"] = np.select(
                H_split_p_cond, H_split_p_res, 0
            )
    
            drivers["L_SRP_for_opening_type"] = np.where(
                (drivers.srp > 0)
                & (drivers.light == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                drivers.L_SRP + drivers["Capping Shelf Cases"] ,
                0,
            ).astype("float32")
            
            drivers["L_SRP_for_opening_type"] = np.where(
                (drivers.split_pallet > 0)
                & (drivers.light == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                 drivers["L_split_pallet_cases_for_opening"],
                drivers["L_SRP_for_opening_type"],
            ).astype("float32")
            
    
    
            drivers["H_SRP_for_opening_type"] = np.where(
                (drivers.srp > 0)
                & (drivers.heavy == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                drivers.H_SRP + drivers["Capping Shelf Cases"],
                0,
            ).astype("float32")
            drivers["H_SRP_for_opening_type"] = np.where(
                (drivers.split_pallet > 0)
                & (drivers.heavy == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                 drivers["H_split_pallet_cases_for_opening"],
                drivers["H_SRP_for_opening_type"],
            ).astype("float32")
            
    
            drivers["L_NSRP_for_opening_type"] = np.where(
                (drivers.nsrp > 0)
                & (drivers.light == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                drivers.L_NSRP + drivers["Capping Shelf Cases"],
                0,
            ).astype("float32")
            drivers["H_NSRP_for_opening_type"] = np.where(
                (drivers.nsrp > 0)
                & (drivers.heavy == 1)
                & (drivers["total_ownbrand_op_type"] == 0)
                & (drivers["opening_type"] != "Returnable Plastic Crate"),
                drivers.H_NSRP + drivers["Capping Shelf Cases"],
                0,
            ).astype("float32")
    
            drivers.drop(
                ["total_ownbrand_op_type", "total_ownbrand_op_cases"], axis=1, inplace=True
            )
    
            drivers["Empty Pallets"] = (
                drivers["Bulk Pallets"] + drivers["Replenished Pallets"]
            )
            drivers["Empty Rollcages"] = drivers["Replenished Rollcages"]
            drivers["Empty Shelf Trolley"] = (
                drivers["Empty Pallets"] * shelf_trolley_cap_ratio_to_pallet
                + drivers["Empty Rollcages"] * shelf_trolley_cap_ratio_to_rollcage
            )
    

            

            
            
            try:
            
                # =============================================================================
                #            PBL PBS settings
                # =============================================================================
    
                
                drivers['Add_Walking PBL Cages'] = drivers["Replenished Rollcages PBL"] + drivers["Replenished Pallets PBL"]
                
                drivers['Add_Walking PBS Cages'] =  drivers["Replenished Rollcages PBS"] 
                
                
                drivers['Add_Walking PBL Pallets'] = 0
                drivers['Add_Walking PBS Pallets'] = drivers["Replenished Pallets PBS"] 
                
                
                
                
                # =============================================================================
                #    PBS/PBL         
                # =============================================================================
                drivers["Add_Walking PBL Pallets"] = np.where(
                    (drivers.full_pallet > 0) | (drivers.mu > 0),
                    0,
                    drivers["Add_Walking PBL Pallets"],
                ).astype("float32")
                
                
                drivers['Add_Walking PBS Pallets'] = np.where(
                    (drivers.full_pallet > 0) | (drivers.mu > 0),
                    drivers["Bulk Pallets"],
                    drivers['Add_Walking PBS Pallets'],
                ).astype("float32")
                
                
                
                
                
            except:
                pass
            ##################################################################################
            
            
            
            drivers["Add_Walking Backstock Cages"] = drivers["Backstock Rollcages"].astype(
                "float32"
            )
            drivers["Add_Walking Cages"] = drivers["Replenished Rollcages"].astype(
                "float32"
            )
            drivers["Add_Walking Pallets"] = np.where(
                (drivers.full_pallet > 0) | (drivers.mu > 0),
                drivers["Bulk Pallets"],
                drivers["Replenished Pallets"],
            ).astype("float32")
            
                                                        
    
            drivers["sold_cases"] = drivers["sold_units"] / drivers["case_capacity"]
            drivers["sold_cases"] = (
                drivers["sold_cases"].replace(np.nan, 0).replace([np.inf, -np.inf], 0)
            )
    
            drivers.rename(columns={"sales_excl_vat": "sales"}, inplace=True)
            
            
            
            
            # Single Pick customization 2
    
            drivers["single_pick_items"] = np.where(drivers.single_pick > 0,
                                         drivers["cases_to_replenish"]  , 0)
            
            drivers["cases_delivered_rsu"] = np.where(drivers.single_pick > 0, 0, drivers['cases_delivered'])
    
                              
    
                    
    
            
            drivers["stock_unit_weekly"] = drivers["stock"]/weekdays_to_divide
            drivers["stock_cases_weekly"] = drivers["stock_unit_weekly"] / drivers.case_capacity
    
            
            
            
            
            drivers['icreamNSRP'] = np.where(drivers.icream_nsrp > 0, drivers.L_NSRP_Items + drivers.H_NSRP_Items, 0 )
            
            for x in ['H_CASE_H_NSRP_items', 'H_CASE_L_NSRP_items', 'L_NSRP_Items', 'H_NSRP_Items']:
                
                drivers[x] = np.where(drivers.icream_nsrp > 0, 0, drivers[x])
                
                
                
            ###########################################################################################
            
            if shelfService_gm == True:
                
                columns_for_shelService_deletion = ["Replenished Rollcages",
                                                    "Replenished Pallets",
                                                    "Backstock Pallets",
                                                    "Backstock Rollcages",
                                                    "Backstock Shelf Trolley",
                                                    "Empty Pallets",
                                                    "Empty Rollcages",
                                                    "Empty Shelf Trolley",
                                                    "Full Pallet",
                                                    "Full Pallet Cases",
                                                    "H_CASE_H_NSRP_items",
                                                    "H_CASE_L_NSRP_items",
                                                    "H_Hook Fill Cases",
                                                    "H_NSRP_for_opening_type",
                                                    "H_SRP",
                                                    "H_NSRP",
                                                    "H_SRP_for_opening_type",
                                                    "Hook Fill Items",
                                                    "L_NSRP",
                                                    "L_NSRP_for_opening_type",
                                                    "L_NSRP_Items",
                                                    "L_SRP",
                                                    "L_SRP_for_opening_type",
                                                    "MU Pallet",
                                                    "New Delivery - Pallets",
                                                    "New Delivery - Rollcages",
                                                    "New Delivery - Shelf Trolley",
                                                    "Total RC's and Pallets",
                                                    "Bulk Pallets",
                                                    "Add_Walking Backstock Cages",
                                                    "Add_Walking Pallets",
                                                    "Racking Pallets",
                                                    'Full + Half Pallet Cases']
                
                
                for x in columns_for_shelService_deletion:
                    
                    drivers[x] = np.where(drivers['shelfservice_flag'] == 1, 0, drivers[x])
                
            
            ############################foil disassemble calc###########################################
            
            
            foilDisassemble_as_is_colname = ['H_NSRP_for_opening_type',
                                              'L_NSRP_for_opening_type',
                                              'L_SRP_for_opening_type',
                                              'H_SRP_for_opening_type',
                                              'Ownbrand_perforated_box_cases',
                                              'Ownbrand_shrink_cases',
                                              'Ownbrand_tray_cases',
                                              'Ownbrand_tray_with_hood_cases',
                                              'Ownbrand_tray_with_shrink_cases']
            
            drivers['total_cases_to_disassemble'] = drivers[foilDisassemble_as_is_colname].sum(axis=1)
            
            drivers['cases_foil_to_disassemble'] =  np.where((drivers['total_cases_to_disassemble'] > 0) & (drivers['Foil_Cases'] > 0),
                                                                      drivers['Foil_Cases'] * drivers['extra disassemble %'], 0)
            
    
            
    
                
    
            
            
                                                                                  
            
                    
            drivers['nr_of_tpn'] = 1 / weekdays_to_divide
            drivers['weekly_stock'] = drivers.stock / weekdays_to_divide
            drivers['shelfCapacity'] = drivers.shelfCapacity / weekdays_to_divide
            drivers['case_capacity'] = drivers.case_capacity / weekdays_to_divide
            drivers["cases_to_replenish"] = drivers["cases_to_replenish"] + drivers["Clip Strip Cases"]
            drivers["pallet_capacity_avg"] = drivers["pallet_capacity"]
            
                        
            drivers = drivers[
                [
                    "country",
                    "store",
                    "day",
                    "dep",
                    "pmg",
                    "t_touch",
                    "cases_to_replenish",
                    "Sec_NSRP_cases",
                    "Sec_SRP_cases",
                    "light",
                    "heavy",
                    "backstock pallet ratio",
                    "Replenished Rollcages",
                    "Replenished Pallets",
                    "tpnb",
                    "sold_units",
                    "sold_cases",
                    "sales",
                    "stock",
                    'weekly_stock',
                    "cases_delivered",
                    "cases_delivered_on_sf",
                    "cases_delivered_rsu",
                    "Add_Walking Cages",
                    "pallet_capacity",
                    "Add_Walking Backstock Cages",
                    "Add_Walking Pallets",
                    'Backstock unit',
                    "Backstock Cases",
                    "Backstock Pallets",
                    "Backstock Rollcages",
                    "Backstock Shelf Trolley",
                    'Backstock_tpn_nr',
                    "backroom_pallets",
                    "Bottle_Tag",
                    "Broken Items",
                    "broken_case_flag",
                    "Bulk Pallets",
                    "Capping Shelf Cases",
                    "Clip Strip Cases",
                    "Clip Strip Items",
                    "Electro_Tag",
                    "Empty Pallets",
                    "Empty Rollcages",
                    "Empty Shelf Trolley",
                    "Foil_Cases",
                    "Full Pallet",
                    "Full Pallet Cases",
                    'Full + Half Pallet Cases',
                    "icreamNSRP",
                    "Gillette_Tag",
                    "H_Post-sort Cases",
                    "H_CASE_H_NSRP_items",
                    "H_CASE_L_NSRP_items",
                    "single_pick_items",
                    "H_Hook Fill Cases",
                    "L_Hook Fill Cases",
                    "H_NSRP_for_opening_type",
                    "H_Pre-sorted Cases",
                    "H_SRP",
                    "H_NSRP",
                    "H_SRP_for_opening_type",
                    #'H_SRP_wo_remain',
                    #'L_SRP_wo_remain',
                    "Hard_Tag",
                    "Hook Fill Items",
                    "L_Post-sort Cases",
                    "L_NSRP",
                    "L_NSRP_for_opening_type",
                    "L_NSRP_Items",
                    "L_Pre-sorted Cases",
                    "L_SRP",
                    "L_SRP_for_opening_type",
                    "MU Pallet",
                    "New Delivery - Pallets",
                    "New Delivery - Rollcages",
                    "New Delivery - Shelf Trolley",
                    "Ownbrand_perforated_box_cases",
                    "Ownbrand_shrink_cases",
                    "Ownbrand_tray_cases",
                    "Ownbrand_tray_with_hood_cases",
                    "Ownbrand_tray_with_shrink_cases",
                    "Pre-sorted Rollcages",
                    "Pre-sorted Shelf Trolley",
                    "Post-sort Cases",
                    "Post-sort Pallets",
                    "Post-sort Rollcages",
                    "Racking Pallets",
                    "Safer_Tag",
                    "Salami_Tag",
                    "Soft_Tag",
                    "CrocoTag",
                    "BliszterfülTag",
                    "Total RC's and Pallets",
                    "High_pallet_cases_on_Dry30_and_DRY24",
                    "High_pallets_on_Dry30_and_DRY24",
                    "High_half_pallet_cases_on_Dry30_and_DRY24",
                    "High_half_pallets_on_Dry30_and_DRY24",
                    "Tag_total_nr",
                    "shelfCapacity",
                    'nr_of_tpn',
                    'unit',
                    'case_capacity',
                    
                    'Two Touch Cases',
                    'Two Touch unit',
                    'total_cases_to_disassemble',
                    'cases_foil_to_disassemble',
    
                    # 'stock_unit_weekly',
                    'stock_cases_weekly',
                    # 'shop_floor_capacity_weekly_unit',
                    # 'shop_floor_capacity_weekly_cases',
                    
                    
                    # 'One Touch Cases',
                    'Add_Walking PBL Cages',
                    'Add_Walking PBS Cages',
                    'Add_Walking PBL Pallets',
                    'Add_Walking PBS Pallets',
                    'PBL_CAGE','PBL_PALLET', 'PBS_CAGE','PBS_PALLET',
                    'pallet_capacity_avg',
                    # 'division_hier','DIV_ID',
                    # 'department','DEP_ID',

                ]
            ]
    
            drivers = optimize_objects(optimize_types(drivers))



        if cases_to_replenish_only == False: 
    
            # PRODUCE
        
            if len(drivers_produce) > 0:
        
                drivers_produce = drivers_produce[
                    [
                        "country",
                        "store",
                        "day",
                        "tpnb",
                        "dep",
                        "pmg",
                        "cases_delivered",
                        "case_capacity",
                        "pallet_capacity",
                        "stock",
                        "sold_units",
                        "sales_excl_vat",
                        "unit_type",
                        "weight",
                        "srp",
                        "nsrp",
                        "full_pallet",
                        "mu",
                        "split_pallet",
                        "icream_nsrp",
                        "unit",
                        "shelfCapacity"

                    ]
                ]
                produce_df = pd.read_excel(directory / excel_inputs_f, "produce_dataframe")
                produce_df.columns = [i.lower() for i in produce_df.columns]
                produce_modules = pd.read_excel(directory / excel_inputs_f, "produce_modules")
                produce_modules.columns = [i.lower() for i in produce_modules.columns]
                RC_table = produce_df[["pmg", "replenishment_type", "rc_capacity"]].copy()
                drivers_produce = drivers_produce.merge(
                    produce_df[produce_df.columns[~produce_df.columns.isin(["rc_capacity"])]],
                    on="pmg",
                    how="left",
                )
                # =============================================================================
                # Crates Customizing + Average Items in Case
                # - custom crates shows a total amount of LARGE CRATES. So, if we have 4 small crates then we treat them as 2 large
                # - daily_crates_on_stock = stock crates + sold crates
                # - items in case is necesary for categories replenished as an item
                # =============================================================================
                # drivers_produce['stock'] = drivers_produce['cases_delivered']
                # drivers_produce['stock'] = ((drivers_produce.groupby(['store', 'tpn'])['stock'].transform("max"))/7).astype("float32")
                drivers_produce["sold_units"] = np.where(
                    (drivers_produce.pmg == "PRO04") | (drivers_produce.pmg == "PRO01"),
                    drivers_produce["sold_units"] * 1,
                    drivers_produce["sold_units"],
                )
                drivers_produce["crates_on_stock"] = (
                    drivers_produce.stock / drivers_produce.case_capacity
                )
                drivers_produce["custom_sold_crates"] = np.where(
                    drivers_produce.unit_type == "KG",
                    drivers_produce.sold_units / (drivers_produce.case_capacity * 1),
                    drivers_produce.sold_units / drivers_produce.case_capacity,
                )
                drivers_produce["custom_sold_crates"] = np.where(
                    (drivers_produce.crate_size == "Small"),
                    drivers_produce.custom_sold_crates / 2,
                    drivers_produce.custom_sold_crates,
                ).astype("float32")
                drivers_produce["custom_sold_crates"] = np.where(
                    (drivers_produce.crate_size == "Other"),
                    drivers_produce.custom_sold_crates / 4,
                    drivers_produce.custom_sold_crates,
                ).astype("float32")
                drivers_produce["custom_stock_crates"] = np.where(
                    (drivers_produce.crate_size == "Small"),
                    drivers_produce.crates_on_stock / 2,
                    drivers_produce.crates_on_stock,
                ).astype("float32")
                drivers_produce["custom_stock_crates"] = np.where(
                    (drivers_produce.crate_size == "Other"),
                    drivers_produce.custom_stock_crates / 4,
                    drivers_produce.custom_stock_crates,
                ).astype("float32")
                # drivers_produce['daily_crates_on_stock'] = drivers_produce.custom_sold_crates + drivers_produce.custom_stock_crates # daily_stock = stock on the end of a day + what they sold this day
                drivers_produce["total_sales_per_repl_type"] = drivers_produce.groupby(
                    ["store", "day", "replenishment_type"], observed=True
                )["sold_units"].transform("sum")
                # =============================================================================
                # Capacity
                # - custom_crates_on_stock = total of daily crates on stock (daily sold crates + daily stock)
                # - custom_tpn_on_stock = total amount of TPNs per pmg and store
                # - multideck_group = in here we check how many crates can be filled on modules. We take into consideration:
                #     1. stock ratio per pmg/store
                #     2. amount of crates per tpn. If we have more than 4 crates per TPN then we put it to warehouse (we have a place just for 4 tpns per one TPN)
                # - backstock = is calculated based on custom_crates_on_stock. So it is daily sold crates + daily stock
                # =============================================================================
                # drivers_produce['crates_per_tpn'] = drivers_produce.daily_crates_on_stock / dataset_group.tpn
                # drivers_produce['daily_crates_on_stock_tpn'] = np.where(drivers_produce.daily_crates_on_stock > 4, 4 * drivers_produce.tpn, drivers_produce.daily_crates_on_stock)
                drivers_produce = drivers_produce.merge(
                    produce_modules[["store", "tables", "multidecks"]], on="store", how="left"
                )
                drivers_produce["one_touch"] = np.where(
                    drivers_produce.replenishment_type == "Multideck",
                    MODULE_CRATES * drivers_produce.multidecks,
                    0,
                )  # dataset_group (banana: 3 crates on hammock + 4 below)
                drivers_produce["one_touch"] = np.where(
                    drivers_produce.replenishment_type == "Produce_table",
                    TABLE_CRATES * drivers_produce.tables,
                    drivers_produce.one_touch,
                )  # calculate shop-floor capacity based on knowledge about total amount of modules
                drivers_produce["one_touch"] = np.where(
                    drivers_produce.replenishment_type == "Stand", 50, drivers_produce.one_touch
                )
                drivers_produce["one_touch"] = np.where(
                    drivers_produce.replenishment_type == "Hammok", 7, drivers_produce.one_touch
                )
                drivers_produce["one_touch"] = np.where(
                    drivers_produce.replenishment_type == "Bin", 50, drivers_produce.one_touch
                )
        
                drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
                    drivers_produce["sold_units"] / drivers_produce["total_sales_per_repl_type"]
                )
                drivers_produce["sales_repl_type_tpn_sales_ratio"] = (
                    drivers_produce["sales_repl_type_tpn_sales_ratio"]
                    .replace(np.nan, 0)
                    .replace([np.inf, -np.inf], 0)
                )
                drivers_produce["one_touch_for_tpns"] = np.ceil(
                    drivers_produce["one_touch"]
                    * drivers_produce["sales_repl_type_tpn_sales_ratio"]
                )
        
                drivers_produce["backstock"] = np.where(
                    drivers_produce.one_touch_for_tpns <= drivers_produce.custom_stock_crates,
                    drivers_produce.custom_stock_crates - drivers_produce.one_touch_for_tpns,
                    0,
                ).astype("float32")
        
                drivers_produce.rename(columns={"sales_excl_vat": "sales"}, inplace=True)
                drivers_produce = drivers_produce[
                    [
                        "country",
                        "store",
                        "day",
                        "dep",
                        "pmg",
                        "tpnb",
                        "stock",
                        "weight",
                        "replenishment_type",
                        "unit_type",
                        "pallet_capacity",
                        "one_touch",
                        "backstock",
                        "cases_delivered",
                        "sold_units",
                        "sales",
                        "custom_sold_crates",
                        "case_capacity",
                        "srp",
                        "nsrp",
                        "full_pallet",
                        "mu",
                        "split_pallet",
                        "icream_nsrp",
                        "unit",
                        "shelfCapacity"

                    ]
                ]
        
                drivers_produce["total_unit_to_fill"] = np.where(
                    (
                        drivers_produce.cases_delivered * drivers_produce.case_capacity
                        + drivers_produce.stock
                    )
                    > drivers_produce.sold_units,
                    drivers_produce.sold_units,
                    (
                        drivers_produce.cases_delivered * drivers_produce.case_capacity
                        + drivers_produce.stock
                    ),
                )
        
                cycle_list = 5
                cycle_list_2 = 2
                cycle_list_1 = 1
        
                unit_cond = [
                    drivers_produce["custom_sold_crates"] >= cycle_list,
                    (drivers_produce["custom_sold_crates"] < cycle_list)
                    & (drivers_produce["custom_sold_crates"] > cycle_list_1),
                    (drivers_produce["custom_sold_crates"] < cycle_list)
                    & (drivers_produce["custom_sold_crates"] < cycle_list_2)
                    & (drivers_produce["custom_sold_crates"] < cycle_list_1),
                ]
                unit_result = [
                    drivers_produce.sold_units / cycle_list,
                    drivers_produce.sold_units / cycle_list_2,
                    drivers_produce.sold_units / cycle_list_1,
                ]
        
                drivers_produce["UNIT_to_replenish_per_rounds"] = np.select(
                    unit_cond, unit_result, 0
                )
        
                crates_cond = [
                    drivers_produce["custom_sold_crates"] >= cycle_list,
                    (drivers_produce["custom_sold_crates"] < cycle_list)
                    & (drivers_produce["custom_sold_crates"] > cycle_list_1),
                    (drivers_produce["custom_sold_crates"] < cycle_list)
                    & (drivers_produce["custom_sold_crates"] < cycle_list_2)
                    & (drivers_produce["custom_sold_crates"] > 0.5),
                    (drivers_produce["custom_sold_crates"] < 0.5),
                ]
        
                crates_result = [
                    (
                        (
                            np.ceil(
                                drivers_produce["UNIT_to_replenish_per_rounds"]
                                / drivers_produce.case_capacity
                            )
                        )
                        * cycle_list
                    ),
                    (
                        (
                            np.ceil(
                                drivers_produce["UNIT_to_replenish_per_rounds"]
                                / drivers_produce.case_capacity
                            )
                        )
                        * cycle_list_2
                    ),
                    (
                        (
                            np.ceil(
                                drivers_produce["UNIT_to_replenish_per_rounds"]
                                / drivers_produce.case_capacity
                            )
                        )
                        * cycle_list_1
                    ),
                    0,
                ]
        
                drivers_produce["CRATES_to_replenish"] = np.select(
                    crates_cond, crates_result, 0
                )
        
                # drivers_produce['shelf_filling'] = drivers_produce.one_touch
                # drivers_produce['crates_to_replenish'] = 0
        
                # for x, y in zip(range(1,cycle_list+1),range(cycle_list)):
        
                #     drivers_produce[f'cycle_{x}']=np.where((((drivers_produce['shelf_filling']-(SALES_CYCLE[y]*drivers_produce['custom_sold_crates']))/drivers_produce['shelf_filling'])<FULFILL_TARGET),1,0)
                #     drivers_produce['crates_to_replenish']=np.where(drivers_produce[f'cycle_{x}']>0, ((drivers_produce['crates_to_replenish']+(drivers_produce['one_touch']-drivers_produce['shelf_filling']))+(drivers_produce['custom_sold_crates']*SALES_CYCLE[y])), drivers_produce['crates_to_replenish'])
                #     drivers_produce['shelf_filling']=np.where(drivers_produce[f'cycle_{x}']>0, drivers_produce['one_touch'], drivers_produce['shelf_filling']-(drivers_produce['custom_sold_crates']*SALES_CYCLE[y]))
        
                # =============================================================================
                # Weekly drivers calculation
                # - backstock_cases_replenished is required just to know how many times I need to move
                # - backstock_rc shows amount of RCs which have to be moved on Shop-Floor (sometimes the same RC needs to be moved more than once). So it is NOT amount of RCs bout amout of stock movements
                # =============================================================================
        
                drivers_produce = drivers_produce.merge(
                    RC_table, on=["pmg", "replenishment_type"], how="left"
                )
                # drivers_produce['one_touch_cases'] = (drivers_produce.one_touch/(drivers_produce.one_touch+drivers_produce.backstock))*drivers_produce.cases_delivered
                drivers_produce["Backstock Cases"] = drivers_produce["backstock"]
                
                drivers_produce["Post-sort Cases"] = drivers_produce["Backstock Cases"]
        
                drivers_produce["backstock_cases_frequency"] = cycle_list
                drivers_produce["backstock_cases_replenished"] = drivers_produce[
                    "backstock"
                ]  # ((drivers_produce.CRATES_to_replenish/drivers_produce.backstock)*drivers_produce['Backstock Cases'])
                drivers_produce["pre_sorted_cases"] = (
                    drivers_produce.backstock_cases_replenished * 0.25
                )  # presort% from Store_Inputs table
                drivers_produce["Pre-sorted Rollcages"] = (
                    drivers_produce.pre_sorted_cases / drivers_produce.rc_capacity
                )
                drivers_produce["one_touch_rc"] = (
                    drivers_produce.cases_delivered / drivers_produce.rc_capacity
                )
                drivers_produce["Backstock Rollcages"] = 0
                drivers_produce["Backstock Pallets"] = np.where(
                    drivers_produce.replenishment_type != "Other",
                    (
                        (
                            drivers_produce.backstock_cases_replenished
                            / drivers_produce.rc_capacity
                        )
                        * RC_CAPACITY
                    )
                    * (1 - RC_DELIVERY),
                    0,
                ).astype("float32")
                drivers_produce["backstock_rc_incl_frequencies"] = (
                    drivers_produce.backstock_cases_replenished
                    / drivers_produce.rc_capacity
                    * drivers_produce.backstock_cases_frequency
                )
                drivers_produce = drivers_produce.replace(np.nan, 0)
                drivers_produce["weight_selector"] = (
                    drivers_produce.weight * drivers_produce.case_capacity
                )  # 1. Heavy & Light
                drivers_produce["heavy_crates"] = np.where(
                    drivers_produce.weight_selector >= 5, 1, 0
                ).astype("int8")
                drivers_produce["light_crates"] = np.where(
                    drivers_produce.weight_selector < 5, 1, 0
                ).astype("int8")
                drivers_produce.drop(["weight_selector"], axis=1, inplace=True)
                drivers_produce["L_Pre-sorted Crates"] = np.where(
                    drivers_produce["light_crates"] == 1, drivers_produce["pre_sorted_cases"], 0
                ).astype("float32")
                drivers_produce["H_Pre-sorted Crates"] = np.where(
                    drivers_produce["heavy_crates"] == 1, drivers_produce["pre_sorted_cases"], 0
                ).astype("float32")
                drivers_produce["L_Post-sort Cases"] = np.where(
                    drivers_produce["light_crates"] == 1, drivers_produce["Post-sort Cases"], 0
                ).astype("float32")
                drivers_produce["H_Post-sort Cases"] = np.where(
                    drivers_produce["heavy_crates"] == 1, drivers_produce["pre_sorted_cases"], 0
                ).astype("float32")
                drivers_produce["New Delivery - Rollcages"] = (
                    drivers_produce.cases_delivered / drivers_produce.rc_capacity
                ) * RC_DELIVERY
                drivers_produce["New Delivery - Pallets"] = np.where(
                    drivers_produce.replenishment_type != "Other",
                    (drivers_produce["New Delivery - Rollcages"] * RC_CAPACITY)
                    * (1 - RC_DELIVERY),
                    0,
                ).astype("float32")
                # drivers_produce['New Delivery - Pallet_cases'] = drivers_produce['New Delivery - Pallets'] * drivers_produce.case_capacity
                # drivers_produce['New Delivery - Rollcages'] = (drivers_produce.cases_delivered-drivers_produce['New Delivery - Pallet_cases']) /drivers_produce.rc_capacity
                drivers_produce["Replenished Rollcages"] = drivers_produce[
                    "New Delivery - Rollcages"
                ]  # Replenished Rollcages and Pallets - it is different than on Repl as we do not pre-sort new delivery on produce (just backstock)
                drivers_produce["Replenished Pallets"] = drivers_produce[
                    "New Delivery - Pallets"
                ]
                drivers_produce["Green crates case fill"] = np.where(
                    (
                        (drivers_produce.replenishment_type == "Multideck")
                        | (drivers_produce.replenishment_type == "Produce_table")
                        | (drivers_produce.replenishment_type == "Hammok")
                    )
                    & (drivers_produce.unit_type == "KG"),
                    drivers_produce.CRATES_to_replenish,
                    0,
                )
                drivers_produce["L_Green crates case fill"] = np.where(
                    drivers_produce["light_crates"] == 1,
                    drivers_produce["Green crates case fill"],
                    0,
                )
                drivers_produce["H_Green crates case fill"] = np.where(
                    drivers_produce["heavy_crates"] == 1,
                    drivers_produce["Green crates case fill"],
                    0,
                )
                drivers_produce["Green crates unit fill"] = np.where(
                    (
                        (drivers_produce.replenishment_type == "Multideck")
                        | (drivers_produce.replenishment_type == "Produce_table")
                        | (drivers_produce.replenishment_type == "Hammok")
                    )
                    & (drivers_produce.unit_type != "KG"),
                    drivers_produce.CRATES_to_replenish,
                    0,
                )
                drivers_produce["Green crates unit fill items"] = np.where(
                    (
                        (drivers_produce.replenishment_type == "Multideck")
                        | (drivers_produce.replenishment_type == "Produce_table")
                        | (drivers_produce.replenishment_type == "Hammok")
                    )
                    & (drivers_produce.unit_type != "KG"),
                    drivers_produce.total_unit_to_fill,
                    0,
                )
                drivers_produce["Bulk Product Cases"] = np.where(
                    drivers_produce.pmg == "PRO14", drivers_produce.custom_sold_crates, 0
                )  # Bulk Seeds / Nuts / Dried Fruits - CASE
                drivers_produce["Backstock_Frequency"] = np.where(
                    drivers_produce.replenishment_type != "Other",
                    drivers_produce.backstock_rc_incl_frequencies / 1,
                    0,
                ).astype("float32")
                drivers_produce["Empty Rollcages"] = drivers_produce[
                    "Backstock Rollcages"
                ]  # we calculate it in wrong way but we did not use it in the model as we calculated it in the model's drivers sheet
                drivers_produce["Empty Pallets"] = drivers_produce["Backstock Pallets"]
                drivers_produce["Potted Plants Cases"] = np.where(
                    drivers_produce.pmg == "PRO13", drivers_produce.CRATES_to_replenish, 0
                )  # Flower + Garden - Tray fill & Unit Fill (potted plant) - herbs
                drivers_produce["Potted Plants Items"] = np.where(
                    drivers_produce.pmg == "PRO13", drivers_produce.total_unit_to_fill, 0
                )
                drivers_produce["Banana Cases"] = np.where(
                    drivers_produce.pmg == "PRO01", drivers_produce.CRATES_to_replenish, 0
                )
                drivers_produce["Banana Shelves Cases"] = np.where(
                    drivers_produce.pmg == "PRO01",
                    drivers_produce.CRATES_to_replenish * 0.57,
                    0,
                )  # Banana_Cases & Banana shelves cases below hammock & Banana Hammock - BUNCH
                drivers_produce["Banana Hammock Bunch"] = np.where(
                    drivers_produce.pmg == "PRO01", drivers_produce.total_unit_to_fill, 0
                )
                drivers_produce["Cut Flowers"] = np.where(
                    drivers_produce.pmg == "PRO17", drivers_produce.CRATES_to_replenish, 0
                )  # Flower + Garden - Cut Flower - CASE= Bucket; Cut Flowers
                drivers_produce["Flowers Rollcages"] = np.where(
                    drivers_produce.pmg == "PRO17",
                    RC_DELIVERY * drivers_produce["one_touch_rc"],
                    0,
                ).astype("float32")
                drivers_produce["Flowers Pallets"] = np.where(
                    drivers_produce.pmg == "PRO17",
                    (
                        drivers_produce["one_touch_rc"]
                        - (drivers_produce["one_touch_rc"] * RC_DELIVERY)
                    )
                    * RC_VS_PAL_CAPACITY,
                    0,
                ).astype("float32")
                drivers_produce["Bulk Pallets"] = 0
                drivers_produce["Total RC's and Pallets"] = (
                    drivers_produce["Replenished Rollcages"]
                    + drivers_produce["Replenished Pallets"]
                    + drivers_produce["Backstock Rollcages"]
                    + drivers_produce["Backstock Pallets"]
                    + drivers_produce["Bulk Pallets"]
                ).astype("float32")
                drivers_produce["Total Green Crates"] = (
                    drivers_produce["Green crates case fill"]
                    + drivers_produce["Green crates unit fill"]
                )
        
                drivers_produce["Add_Walking Backstock Cages"] = (
                    drivers_produce["Backstock Rollcages"]
                ).astype("float32")
                drivers_produce["Add_Walking Cages"] = (
                    drivers_produce["Replenished Rollcages"]
                ).astype("float32")
                drivers_produce["Add_Walking Pallets"] = drivers_produce["Replenished Pallets"]
        
                drivers_produce.replace([np.inf, -np.inf], 0, inplace=True)
                drivers_produce.replace(np.nan, 0, inplace=True)
                drivers_produce['cases_delivered_rsu'] = drivers_produce["cases_delivered"]
                
                drivers_produce["Post-sort Rollcages"] = drivers_produce["Backstock Rollcages"]
                drivers_produce["Post-sort Pallets"] = drivers_produce["Backstock Pallets"]
                
                drivers_produce["weekly_stock"] = drivers_produce["stock"] / 7 
                drivers_produce["case_capacity"] = drivers_produce["case_capacity"] / 7
                drivers_produce["shelfCapacity"] = drivers_produce["shelfCapacity"] / 7
                drivers_produce = drivers_produce[
                    [
                        "country",
                        "store",
                        "day",
                        "dep",
                        "pmg",
                        "tpnb",
                        "sold_units",
                        "sales",
                        "stock",
                        "cases_delivered",
                        "cases_delivered_rsu",
                        "Backstock Rollcages",
                        "Backstock Cases",
                        "Post-sort Cases",
                        "L_Post-sort Cases",
                        "H_Post-sort Cases",
                        "Backstock Pallets",
                        "Banana Cases",
                        "Banana Shelves Cases",
                        "Banana Hammock Bunch",
                        "Cut Flowers",
                        "Flowers Rollcages",
                        "Flowers Pallets",
                        "Bulk Pallets",
                        "Total RC's and Pallets",
                        "Total Green Crates",
                        "Add_Walking Backstock Cages",
                        "Add_Walking Cages",
                        "Add_Walking Pallets",
                        "Potted Plants Cases",
                        "Potted Plants Items",
                        "Post-sort Pallets",
                        "Post-sort Rollcages",
                        "Empty Rollcages",
                        "Empty Pallets",
                        "New Delivery - Rollcages",
                        "New Delivery - Pallets",
                        "Green crates case fill",
                        "L_Green crates case fill",
                        "H_Green crates case fill",
                        "Green crates unit fill",
                        "Green crates unit fill items",
                        "Bulk Product Cases",
                        "Pre-sorted Rollcages",
                        "L_Pre-sorted Crates",
                        "H_Pre-sorted Crates",
                        "unit",
                        "CRATES_to_replenish",
                        "case_capacity",
                        "shelfCapacity",
                        "weekly_stock"
                    ]
                ]
                drivers_produce = optimize_objects(optimize_types(drivers_produce))
        
            if len(drivers_produce) == 0:
                drivers_produce = drivers_produce[["store", "tpnb"]]
        
            return drivers_produce, drivers
    
        if cases_to_replenish_only == True:
            return  cases_to_replenish_tpn, cases_to_replenish_dep
