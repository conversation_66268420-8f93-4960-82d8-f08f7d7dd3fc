
import pandas as pd
import pyodbc
import numpy as np
from datetime import datetime



pd.set_option("display.max_columns", None)
pd.set_option("display.max_colwidth", None)


# start = '20240701'
# end = '20240707'

# # Define the start and end dates
# start_date = datetime.strptime(start, '%Y%m%d')
# end_date = datetime.strptime(end, '%Y%m%d')

# # Calculate the difference between the two dates
# difference = end_date - start_date

# # Convert the difference into weeks
# weeks = (difference.days + 6) // 7


start = 20250630
end = 20250720

start_date = "'f2025w03'"
end_date = "'f2025w07'"
nr_weeks = int(end_date[7:9]) - int(start_date[7:9]) + 1
df_f = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_0414\item_sold_plano_0414_hier"
a = pd.read_parquet(df_f)
products = a[['country', 'tpnb']].drop_duplicates().groupby(["country"],observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()


# products = {"SK":[2002020322517, 2002020319280, 2002020312286]}
for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))



with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    
    # df2 = pd.DataFrame()

    # for k, v in products.items():
                        
    #     s = list()
        
    #     for x in v:
                    
    #         s.append(str(x))
    #         tpnbs = ",".join(s)
    #         tpnbs = "(" + ",".join([f"'{item}'" for item in s]) + ")"
            
            
            
            
        # promo = f"""
            
            
        #     select 


        #                 a.p1pt_cntr_code as country,
        #                 b.slad_tpnb as tpnb,
                        
        #                 b.slad_long_des as product_name,
                        
                        
        #                 Count(b.slad_tpnb) as number_of_promo_weeks
                        
                        
                        
        #                 from dw.p1p_promo_tpn a
                        
        #                 JOIN
        #                     dm.dim_artgld_details b
        #                     ON
        #                         a.p1pt_cntr_id = b.cntr_id AND
        #                         a.p1pt_dmat_id = b.slad_dmat_id
                                
                                
        #                 where 
                        
        #                 DATE_FORMAT(a.p1pt_pp_start, 'yyyyMMdd') >= {start} and
        #                 DATE_FORMAT(a.p1pt_pp_end, 'yyyyMMdd') <= {end}
        #                 --AND b.cntr_code = '{k}'
        #                 --AND b.slad_tpnb in {tpnbs}
                        
                        
        #                 group by a.p1pt_cntr_code, b.slad_tpnb, b.slad_long_des

            
            
            
            
            
        #     """
        # a = pd.read_sql(promo, conn)
            
            
#         promo_sold = """
        
        
# -- Use CTEs instead of cached tables
# WITH 
# -- Sales data
# sales AS (
#     SELECT s.slsms_cntr_id cntr_id,
#            st.dmst_store_id dmst_id,
#            d.pmg as pmg, 
#            sum(s.slsms_unit_cs) units_ty,
#            sum(case when category_value = 1 then s.slsms_unit_cs
#                     else 0 end) units_ty_promo
#     FROM   dw.sl_sms s
#         INNER JOIN dm.dim_rc_retprice_promo pr
#             ON pr.cntr_id = s.slsms_cntr_id and
#                pr.rc_retprice = s.slsms_nrc
#         INNER JOIN dm.dim_artrep_details a
#             ON a.cntr_id = s.slsms_cntr_id and
#                a.slad_dmat_id = s.slsms_dmat_id
#         INNER JOIN dm.dim_stores st
#             ON s.slsms_cntr_id = st.cntr_id and
#                s.slsms_dmst_id = st.dmst_store_id
#         RIGHT JOIN tesco_analysts.hierarchy_spm d 
#             ON a.dmat_div_code = LPAD(d.div_code,4,"0")
#                AND a.dmat_dep_code = LPAD(d.dep_code,4,"0")
#                AND a.dmat_sec_code = LPAD(d.sec_code,4,"0")
#                AND a.dmat_grp_code = LPAD(d.grp_code,4,"0")
#                AND a.dmat_sgr_code = LPAD(d.sgr_code,4,"0")   
#     WHERE  s.part_col between 20240527 and 20240901
#        and ((s.slsms_salex_cs <> 0 and s.slsms_salex_cs is not null)
#         or (s.slsms_margin <> 0 and s.slsms_margin is not null)
#         or (s.slsms_tr_custnum <> 0 and s.slsms_tr_custnum is not null))
#        and s.slsms_cntr_id in (1, 2, 4)
#     GROUP BY s.slsms_cntr_id, st.dmst_store_id, d.pmg
# ),

# -- Stock data
# stock AS (
#     SELECT s.slstks_cntr_id cntr_id,
#            st.dmst_store_id dmst_id,
#            d.pmg as pmg,
#            sum(s.slstks_stock_unit_sl) stk_units_store_sl_ty
#     FROM   dw.sl_stocks s
#         INNER JOIN dm.dim_artrep_details a
#             ON a.cntr_id = s.slstks_cntr_id and
#                a.slad_dmat_id = s.slstks_dmat_id
#         INNER JOIN dm.dim_stores st
#             ON s.slstks_cntr_id = st.cntr_id and
#                s.slstks_dmst_id = st.dmst_store_id
#         RIGHT JOIN tesco_analysts.hierarchy_spm d 
#             ON a.dmat_div_code = LPAD(d.div_code,4,"0")
#                AND a.dmat_dep_code = LPAD(d.dep_code,4,"0")
#                AND a.dmat_sec_code = LPAD(d.sec_code,4,"0")
#                AND a.dmat_grp_code = LPAD(d.grp_code,4,"0")
#                AND a.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
#     WHERE  s.part_col between 20240527 and 20240901
#        and (s.slstks_stock_unit_sl <> 0
#         or s.slstks_stock_value_gd <> 0
#         or s.slstks_stock_unit_gd <> 0)
#        and (s.slstks_stock_unit_sl is not null
#         or s.slstks_stock_value_gd is not null
#         or s.slstks_stock_unit_gd is not null)
#        and s.slstks_cntr_id in (1, 2, 4)
#     GROUP BY s.slstks_cntr_id, st.dmst_store_id, d.pmg
# ),

# -- Exchange rate data
# rate AS (
#     SELECT cast(x.dmexr_cntr_id as int) cntr_id,
#            x.dmexr_rate xrate
#     FROM   dw.dim_exchange_rates x
#     WHERE  x.dmexr_dmtm_fy_code in (SELECT max(dmexr_dmtm_fy_code)
#                                     FROM   dw.dim_exchange_rates
#                                     WHERE  dmexr_src = 'FIN')
#        and x.dmexr_cntr_id <= 4
#        and x.dmexr_src = 'FIN'
# ),

# -- Union for distinct store-pmg combinations
# union_distinct AS (
#     (SELECT cntr_id, dmst_id, pmg
#      FROM   stock)
#     UNION DISTINCT 
#     (SELECT cntr_id, dmst_id, pmg
#      FROM   sales)
# )

# -- Final query joining all tables
# SELECT c.dmct_code cntr_code,
#        st.dmst_store_code,
#        st.dmst_store_des,
#        st.slsp_region,
#        st.slsp_cluster1,
#        u.pmg,
#        s.units_ty,
#        s.units_ty_promo,
#        stk.stk_units_store_sl_ty
# FROM   union_distinct u
#     INNER JOIN dw.dim_countries c
#         ON c.dmct_id = u.cntr_id
#     INNER JOIN rate xr
#         ON xr.cntr_id = u.cntr_id
#     INNER JOIN dm.dim_stores st
#         ON st.cntr_id = u.cntr_id and
#            st.dmst_store_id = u.dmst_id
#     LEFT JOIN sales s
#         ON s.cntr_id = u.cntr_id and
#            s.dmst_id = u.dmst_id and
#            s.pmg = u.pmg
#     LEFT JOIN stock stk
#         ON stk.cntr_id = u.cntr_id and
#            stk.dmst_id = u.dmst_id and
#            stk.pmg = u.pmg;
        
        
        
#         """

            
            
#         a = pd.read_sql(promo_sold, conn)            
            
            


# with pyodbc.connect(
#     "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
# ) as conn:
    
    
    
    
    
    
    
    
#     promo = """
    
    
#     select 


#                 a.p1pt_cntr_code as country,
#                 b.slad_tpnb as tpnb,
                
#                 b.slad_long_des as product_name,
                
                
#                 DATE_FORMAT(a.p1pt_pp_start, 'yyyyMMdd') AS start_date,
                
#                 a.p1pt_pp_end as end_date
                
                
                
#                 from dw.p1p_promo_tpn a
                
#                 JOIN
#                     dm.dim_artgld_details b
#                     ON
#                         a.p1pt_cntr_id = b.cntr_id AND
#                         a.p1pt_dmat_id = b.slad_dmat_id
                        
                        
#                 where b.slad_tpnb = 203207723 AND
                
#                 a.p1pt_pp_start > '2025-01-01'
                
#                 limit 10
    
    
    
    
    
#     """
    
    
    
#     a = pd.read_sql(promo, conn)        
    
    
    
#     visszaaru = """
    
    
#     --List of Store_nr: ##### (exampla: 41670)

# --list of date: ######## (example: 20250224)

# --List of RC_code: '#', '#' (example: '2', '3')



# with base_data as (

# Select a.part_col as day, a.day as time, a.site as site, a.pon as till, a.opn as opn, a.tin as Tr_Nr,  a.qty_sku as qty_sku, a.qty as qty, (a.amo/100) as sale, a.ean as ean, d.slad_tpn as tpn, d.slad_tpnb as tpnb, d.slad_sh_des as p_name, a.rett as RC_code

# from poshu.t001 a

# LEFT JOIN dm.dim_artgld_details d

# ON a.ean = lpad(d.slem_ean,14,0)

# and d.cntr_code = 'HU'

# Where 

# --and rett in (${List_of_RC_code})    --választható

# a.rett <> 0               --minden visszáru

# and a.part_col BETWEEN 20250324  and 20250330

# )

# select 

# b.day, b.time, b.site, b.till, b.opn, b.Tr_Nr, b.qty_sku, b.qty, b.sale, b.ean, b.tpn, b.tpnb, b.p_name, b.RC_code, c.name as RC_name

# from base_data b

# left join tesco_analysts.ref_code3_fulop_z c

# on b.RC_code = c.id;
 
    
    
#     """
    
    
    
    
#     a = pd.read_sql(visszaaru, conn)    
    
    
    
    
    
    
    
    
    
#     stock_hier =     """
#                       SELECT a.cntr_code AS country_code,
#                               b.part_col as day, 
#                               d.dmst_store_code AS site,
#                               a.dmat_div_des_en AS division,
#                               cast(a.dmat_div_code as INT) as DIV_ID,
#                               a.dmat_dep_des_en AS department,
#                               cast(a.dmat_dep_code as INT) as DEP_ID,
#                               a.dmat_sec_des_en AS section,
#                               cast(a.dmat_sec_code as INT) as SEC_ID,
#                               a.dmat_grp_des_en AS group,
#                               cast(a.dmat_grp_code as INT) as GRP_ID,
#                               a.dmat_sgr_des_en AS subgroup,
#                               cast(a.dmat_sgr_code as INT) as SGR_ID,

#                               Count(distinct(a.slad_tpnb)) AS product_stocked,
#                               SUM(b.slstks_stock_unit) AS items_stocked
#                       FROM dm.dim_artgld_details a
#                       JOIN dw.sl_stocks b ON a.slad_dmat_id = b.slstks_dmat_id
#                       AND a.cntr_id = b.slstks_cntr_id
#                       JOIN dm.dim_stores d ON b.slstks_dmst_id = d.dmst_store_id
#                       AND b.slstks_cntr_id = d.cntr_id
#                       WHERE b.part_col = 20250329
#                         AND Nvl(b.slstks_stock_unit, 0) + Nvl(b.slstks_stock_unit_gd, 0) + Nvl(b.slstks_stock_unit_sl, 0) > 0
#                         AND a.cntr_code = 'HU'
#                         --AND dmtm_code LIKE "c%" 
#                         AND b.slstks_stock_unit > 0
#                       GROUP BY a.cntr_code,
#                                 b.part_col,
#                                 d.dmst_store_code,
#                                 a.dmat_div_des_en,
#                                 a.dmat_div_code,
#                                 a.dmat_dep_des_en,
#                                 a.dmat_dep_code,
#                                 a.dmat_sec_des_en,
#                                 a.dmat_sec_code,
#                                 a.dmat_grp_des_en,
#                                 a.dmat_grp_code,
#                                 a.dmat_sgr_des_en,
#                                 a.dmat_sgr_code
                                
                                
#                                 """
                                
                                
                                
#     a = pd.read_sql(stock_hier, conn)    
                    
    
    
    
    
    sold_and_delivered_units = f"""
    
    
WITH sold_units AS (
    SELECT
        mstr.cntr_code AS country,
        CAST(stores.dmst_store_code AS INT) AS store,  
        cal.dmtm_fw_code AS week,
        SUBSTRING(d.pmg, 1, 3) AS dep,
        mstr.slad_tpnb as tpnb,
        mstr.slad_long_des as product_name,
        SUM(sunit.slsms_unit) AS sold_units
    FROM dw.sl_sms sunit 
    JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
    JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
    JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code,4,"0") 
        AND mstr.dmat_dep_code = LPAD(d.dep_code,4,"0") 
        AND mstr.dmat_sec_code = LPAD(d.sec_code,4,"0") 
        AND mstr.dmat_grp_code = LPAD(d.grp_code,4,"0") 
        AND mstr.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    WHERE stores.cntr_code IN ("SK", "CZ","HU")
        AND sunit.part_col BETWEEN {start} AND {end}
        --AND d.pmg != 'HDL01'
        --AND SUBSTRING(d.pmg, 1, 3) = 'HDL'
        --AND mstr.dmat_sgr_des_en = 'gift_packs'
        AND mstr.slad_tpnb IN (105381925, 111267649)
    GROUP BY mstr.cntr_code, 
    stores.dmst_store_code,
    cal.dmtm_fw_code,
    SUBSTRING(d.pmg, 1, 3),
    mstr.slad_tpnb,
    mstr.slad_long_des
),
delivered_units AS (
    SELECT 
        CASE 
            WHEN a.int_cntr_id = 1 THEN 'CZ'
            WHEN a.int_cntr_id = 2 THEN 'SK'
            WHEN a.int_cntr_id = 4 THEN 'HU'
            ELSE 'Unknown'
        END AS country,
        CAST(CONCAT(a.int_cntr_id, a.store) AS INT) AS store,
        cal.dmtm_fw_code AS week,
        SUBSTRING(d.pmg, 1, 3) AS dep,
        b.slad_tpnb as tpnb,
        b.slad_long_des as product_name,
        SUM(a.qty) AS unit_delivered,
        SUM(a.qty /b.slad_case_size ) as cases_delivered
    FROM stg_go.go_106_order_receiving a
    LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product AND b.cntr_id = a.int_cntr_id
    RIGHT JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = LPAD(d.div_code,4,"0")
        AND b.dmat_dep_code = LPAD(d.dep_code,4,"0")
        AND b.dmat_sec_code = LPAD(d.sec_code,4,"0")
        AND b.dmat_grp_code = LPAD(d.grp_code,4,"0")
        AND b.dmat_sgr_code = LPAD(d.sgr_code,4,"0")
    LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
    WHERE a.int_cntr_code IN (4)
        AND a.part_col BETWEEN {start} AND {end}
        --AND d.pmg != 'HDL01'
        --AND SUBSTRING(d.pmg, 1, 3) = 'HDL'
        AND b.dmat_sgr_des_en = 'gift_packs'
    GROUP BY  
        CASE 
            WHEN a.int_cntr_id = 1 THEN 'CZ'
            WHEN a.int_cntr_id = 2 THEN 'SK'
            WHEN a.int_cntr_id = 4 THEN 'HU'
            ELSE 'Unknown'
        END,
        CONCAT(a.int_cntr_id, a.store), 
        cal.dmtm_fw_code,
        SUBSTRING(d.pmg, 1, 3),
        b.slad_tpnb,
        b.slad_long_des
)
SELECT 
    COALESCE(s.country, d.country) AS country,
    COALESCE(s.store, d.store) AS store,
    COALESCE(s.week, d.week) AS week,
    COALESCE(s.dep, d.dep) AS dep,
    COALESCE(s.tpnb, d.tpnb) AS tpnb,
    COALESCE(s.product_name, d.product_name) AS product_name,
    COALESCE(s.sold_units, 0) AS sold_units,
    COALESCE(d.unit_delivered, 0) AS unit_delivered,
    COALESCE(d.cases_delivered, 0) AS cases_delivered
FROM sold_units s
FULL OUTER JOIN delivered_units d
    ON s.country = d.country
    AND s.store = d.store
    AND s.week = d.week
    AND s.dep = d.dep
    AND s.tpnb = d.tpnb
    AND s.product_name = d.product_name
ORDER BY 
    country,
    store,
    week,
    dep              
               
        
    """
           
    b = pd.read_sql(sold_and_delivered_units, conn)
    
    
    
    # soldunit_items_stock =     f"""
                    
    #                 WITH items_stocked AS (
    #                   SELECT a.cntr_code AS country_code,
    #                          d.dmst_store_code AS site,
    #                          c.pmg as pmg,
    #                          b.part_col as day, 
    #                          dt.dmtm_code AS week,
    #                          a.slad_tpnb as tpnb,
    #                          a.slad_vpn as vpn, 
    #                          a.slad_long_des as product_name,
    #                          Count(distinct(a.slad_tpnb)) AS product_stocked,
    #                          Count(distinct(a.slad_vpn)) AS vpn_stocked,

    #                          SUM(b.slstks_stock_unit) AS items_stocked
    #                   FROM dm.dim_artgld_details a
    #                   JOIN dw.sl_stocks b ON a.slad_dmat_id = b.slstks_dmat_id
    #                   AND a.cntr_id = b.slstks_cntr_id
    #                   JOIN dm.dim_stores d ON b.slstks_dmst_id = d.dmst_store_id
    #                   AND b.slstks_cntr_id = d.cntr_id
    #                   JOIN tesco_analysts.hierarchy_spm c ON a.dmat_div_code = Lpad(c.div_code, 4, '0')
    #                   AND a.dmat_dep_code = Lpad(c.dep_code, 4, '0')
    #                   AND a.dmat_sec_code = Lpad(c.sec_code, 4, '0')
    #                   AND a.dmat_grp_code = Lpad(c.grp_code, 4, '0')
    #                   AND a.dmat_sgr_code = Lpad(c.sgr_code, 4, '0')
    #                   JOIN dw.dim_time dt ON b.slstks_day BETWEEN dt.dmtm_from AND dt.dmtm_to
    #                   WHERE b.part_col BETWEEN {start} AND {end}
    #                     AND Nvl(b.slstks_stock_unit, 0) + Nvl(b.slstks_stock_unit_gd, 0) + Nvl(b.slstks_stock_unit_sl, 0) > 0
    #                     -- AND date_format(b.slstks_day, 'u') = 1
    #                     AND dt.dmtm_dmhm_id=0
    #                     AND dt.dmtm_lvl=1
    #                     AND SUBSTRING(c.pmg, 1, 3) = 'CLG'
    #                     AND d.dmst_store_code = 21012
    #                     AND dmtm_code LIKE "c%" 
    #                     AND b.slstks_stock_unit > 0
    #                   GROUP BY a.cntr_code,
    #                            d.dmst_store_code,
    #                            dt.dmtm_code,
    #                            c.pmg,
    #                            b.part_col,
    #                            a.slad_long_des,
    #                            a.slad_tpnb,
    #                            a.slad_vpn
    #                 ),
                                                           
    #                 sold_units AS (
    #                   SELECT
    #                       mstr.cntr_code AS country,
    #                       CAST(stores.dmst_store_code AS INT) AS site,  
    #                       sunit.part_col AS day,
    #                       d.pmg AS pmg,
    #                         mstr.slad_tpnb as tpnb,
    #                         mstr.slad_vpn as vpn,
    #                         mstr.slad_long_des as product_name,
    #                       SUM(sunit.slsms_unit) AS sold_units
    #                   FROM dw.sl_sms sunit 
    #                   JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
    #                   JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
    #                   JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code,4,"0") 
    #                       AND mstr.dmat_dep_code = LPAD(d.dep_code,4,"0") 
    #                       AND mstr.dmat_sec_code = LPAD(d.sec_code,4,"0") 
    #                       AND mstr.dmat_grp_code = LPAD(d.grp_code,4,"0") 
    #                       AND mstr.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
    #                   LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    #                   WHERE stores.cntr_code IN ("SK", "CZ","HU")
    #                       AND sunit.part_col BETWEEN {start} AND {end}
    #                       AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
    #                      AND stores.dmst_store_code = 21012
    #                      AND sunit.slsms_unit > 0
    #                   GROUP BY mstr.cntr_code, stores.dmst_store_code, sunit.part_col, d.pmg, mstr.slad_tpnb, mstr.slad_long_des, mstr.slad_vpn
    #                 )
                    
    #                 SELECT 
    #                   COALESCE(s.country, i.country_code) AS country,
    #                   COALESCE(s.site, i.site) AS store,
    #                   COALESCE(s.day, i.day) AS day,
    #                   COALESCE(s.pmg, i.pmg) AS pmg,
    #                   COALESCE(s.tpnb, i.tpnb) AS tpnb,
    #                   COALESCE(s.vpn, i.vpn) AS vpn,
    #                   COALESCE(s.product_name, i.product_name) AS product_name,
    #                   COALESCE(s.sold_units, 0) AS sold_units,
    #                   COALESCE(i.product_stocked, 0) AS product_stocked,
    #                   COALESCE(i.vpn_stocked, 0) AS vpn_stocked,
    #                   COALESCE(i.items_stocked, 0) AS items_stocked
    #                 FROM sold_units s
    #                 FULL OUTER JOIN items_stocked i
    #                   ON s.country = i.country_code
    #                   AND s.site = i.site
    #                   AND s.day = i.day
    #                   AND s.pmg = i.pmg
    #                   AND s.tpnb = i.tpnb
    #                   AND s.vpn = i.vpn
    #                   AND s.product_name = i.product_name
    #                 ORDER BY 
    #                   country,
    #                   store,
    #                   day,
    #                   pmg
                    
                    
                    
    # """    
    # b = pd.read_sql(soldunit_items_stock, conn)   
    
    
    
    
#     clg_items_stock = f"""
    
    
# WITH items_stocked AS (
#   SELECT a.cntr_code AS country_code,
#          d.dmst_store_code AS site,
#          c.pmg as pmg,
#          b.part_col as day, 
#          dt.dmtm_code AS week,
#          a.slad_tpnb as tpnb,
#          a.slad_vpn as vpn, 
#          a.slad_long_des as product_name,
#          Count(distinct(a.slad_tpnb)) AS product_stocked,
#          -- For vpn_stocked, we'll handle this differently in the final query
#          SUM(b.slstks_stock_unit) AS items_stocked,
#          -- Adding row number for each unique VPN within the group
#          ROW_NUMBER() OVER (PARTITION BY a.cntr_code, d.dmst_store_code, b.part_col, c.pmg, a.slad_vpn 
#                            ORDER BY a.slad_tpnb) as vpn_row_num
#   FROM dm.dim_artgld_details a
#   JOIN dw.sl_stocks b ON a.slad_dmat_id = b.slstks_dmat_id
#   AND a.cntr_id = b.slstks_cntr_id
#   JOIN dm.dim_stores d ON b.slstks_dmst_id = d.dmst_store_id
#   AND b.slstks_cntr_id = d.cntr_id
#   JOIN tesco_analysts.hierarchy_spm c ON a.dmat_div_code = Lpad(c.div_code, 4, '0')
#   AND a.dmat_dep_code = Lpad(c.dep_code, 4, '0')
#   AND a.dmat_sec_code = Lpad(c.sec_code, 4, '0')
#   AND a.dmat_grp_code = Lpad(c.grp_code, 4, '0')
#   AND a.dmat_sgr_code = Lpad(c.sgr_code, 4, '0')
#   JOIN dw.dim_time dt ON b.slstks_day BETWEEN dt.dmtm_from AND dt.dmtm_to
#   WHERE b.part_col BETWEEN {start} AND {end}
#     AND Nvl(b.slstks_stock_unit, 0) + Nvl(b.slstks_stock_unit_gd, 0) + Nvl(b.slstks_stock_unit_sl, 0) > 0
#     -- AND date_format(b.slstks_day, 'u') = 1
#     AND dt.dmtm_dmhm_id=0
#     AND dt.dmtm_lvl=1
#     AND SUBSTRING(c.pmg, 1, 3) = 'CLG'
#     AND d.dmst_store_code = 21012
#     AND dmtm_code LIKE "c%" 
#     AND b.slstks_stock_unit > 0
#   GROUP BY a.cntr_code,
#            d.dmst_store_code,
#            dt.dmtm_code,
#            c.pmg,
#            b.part_col,
#            a.slad_long_des,
#            a.slad_tpnb,
#            a.slad_vpn
# ),
                                                 
# sold_units AS (
#   SELECT
#       mstr.cntr_code AS country,
#       CAST(stores.dmst_store_code AS INT) AS site,  
#       sunit.part_col AS day,
#       d.pmg AS pmg,
#       mstr.slad_tpnb as tpnb,
#       mstr.slad_vpn as vpn,
#       mstr.slad_long_des as product_name,
#       SUM(sunit.slsms_unit) AS sold_units
#   FROM dw.sl_sms sunit 
#   JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
#   JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
#   JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code,4,"0") 
#       AND mstr.dmat_dep_code = LPAD(d.dep_code,4,"0") 
#       AND mstr.dmat_sec_code = LPAD(d.sec_code,4,"0") 
#       AND mstr.dmat_grp_code = LPAD(d.grp_code,4,"0") 
#       AND mstr.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
#   LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
#   WHERE stores.cntr_code IN ("SK", "CZ","HU")
#       AND sunit.part_col BETWEEN {start} AND {end}
#       AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
#       AND stores.dmst_store_code = 21012
#       AND sunit.slsms_unit > 0
#   GROUP BY mstr.cntr_code, stores.dmst_store_code, sunit.part_col, d.pmg, mstr.slad_tpnb, mstr.slad_long_des, mstr.slad_vpn
# )

# SELECT 
#   COALESCE(s.country, i.country_code) AS country,
#   COALESCE(s.site, i.site) AS store,
#   COALESCE(s.day, i.day) AS day,
#   COALESCE(s.pmg, i.pmg) AS pmg,
#   COALESCE(s.tpnb, i.tpnb) AS tpnb,
#   COALESCE(s.vpn, i.vpn) AS vpn,
#   COALESCE(s.product_name, i.product_name) AS product_name,
#   COALESCE(s.sold_units, 0) AS sold_units,
#   COALESCE(i.product_stocked, 0) AS product_stocked,
#   -- Only count the VPN once (when vpn_row_num = 1)
#   CASE WHEN i.vpn_row_num = 1 THEN 1 ELSE 0 END AS vpn_stocked,
#   COALESCE(i.items_stocked, 0) AS items_stocked
# FROM sold_units s
# FULL OUTER JOIN items_stocked i
#   ON s.country = i.country_code
#   AND s.site = i.site
#   AND s.day = i.day
#   AND s.pmg = i.pmg
#   AND s.tpnb = i.tpnb
#   AND s.vpn = i.vpn
#   AND s.product_name = i.product_name
# ORDER BY 
#   country,
#   store,
#   day,
#   pmg

    
    
#     """
    
#     b = pd.read_sql(clg_items_stock, conn)   




    vpn_clg_pmg = f"""
    
    
    SELECT country_code,
       site,
       pmg,
       Avg(tmp.product_stocked) AS product_stocked,
       AVG(tmp.vpn_stocked) AS vpn_stocked,
       Avg(tmp.items_stocked) AS items_stocked
FROM
    
                         ( SELECT a.cntr_code AS country_code,
                                  d.dmst_store_code AS site,
                                  b.part_col as day,
                                  c.pmg as pmg,
                                  Count(distinct(a.slad_tpnb)) AS product_stocked,
                                  Count(distinct(a.slad_vpn)) AS vpn_stocked,
                                  SUM(b.slstks_stock_unit) AS items_stocked
                          FROM dm.dim_artgld_details a
                          JOIN dw.sl_stocks b ON a.slad_dmat_id = b.slstks_dmat_id
                          AND a.cntr_id = b.slstks_cntr_id
                          JOIN dm.dim_stores d ON b.slstks_dmst_id = d.dmst_store_id
                          AND b.slstks_cntr_id = d.cntr_id
                          JOIN tesco_analysts.hierarchy_spm c ON a.dmat_div_code = Lpad(c.div_code, 4, '0')
                          AND a.dmat_dep_code = Lpad(c.dep_code, 4, '0')
                          AND a.dmat_sec_code = Lpad(c.sec_code, 4, '0')
                          AND a.dmat_grp_code = Lpad(c.grp_code, 4, '0')
                          AND a.dmat_sgr_code = Lpad(c.sgr_code, 4, '0')
                          JOIN dw.dim_time dt ON b.slstks_day BETWEEN dt.dmtm_from AND dt.dmtm_to
                          WHERE b.part_col BETWEEN {start} AND {end}
                            AND Nvl(b.slstks_stock_unit, 0) + Nvl(b.slstks_stock_unit_gd, 0) + Nvl(b.slstks_stock_unit_sl, 0) > 0
                            AND dt.dmtm_dmhm_id=0
                            AND dt.dmtm_lvl=1
                            --AND date_format(b.slstks_day, 'u') = 1
                        AND date_format(b.slstks_day, 'E') = 'Mon'
                            --AND dayofweek(b.slstks_day) = 2
                            AND SUBSTRING(c.pmg, 1, 3) = 'CLG'
                            AND dmtm_code LIKE "c%" 
                            AND b.slstks_stock_unit > 0
                            --AND d.dmst_store_code = 21012
                          GROUP BY a.cntr_code,
                          b.part_col,
                                    d.dmst_store_code,
                                    c.pmg
                                    
                                    
                                    ) AS tmp GROUP BY
                         country_code,
                                                               site,
                                                               pmg;       

    
    
    
    """
    c = pd.read_sql(vpn_clg_pmg, conn)       
    
    
#     cases_delivered = f"""
    
    
# SELECT tmp.cntr_code,
#        CONCAT(YEAR(tmp.orsrec_date), LPAD(WEEKOFYEAR(tmp.orsrec_date), 2, '0')) AS week,
#        tmp.site,
#        pmg,
#        SUM(tmp.cases) AS cases
# FROM (
#     SELECT a.cntr_code,
#            d.dmst_store_code AS site,
#            b.orsrec_date,
#            SUM(orsrec_acc_quant) AS cases,
#            a.dmat_div_code,
#            a.dmat_dep_code,
#            a.dmat_sec_code,
#            a.dmat_grp_code,
#            a.dmat_sgr_code
#     FROM dm.dim_artgld_details a
#     JOIN dw.ors_receivings b
#         ON a.slad_dmat_id = b.orsrec_dmat_id
#         AND a.cntr_id = b.orsrec_cntr_id
#     JOIN dm.dim_stores d
#         ON b.orsrec_dmst_id = d.dmst_store_id
#         AND b.orsrec_cntr_id = d.cntr_id
#     WHERE part_col BETWEEN 20240301 AND 20241124
#     GROUP BY a.cntr_code,
#              b.orsrec_date,
#              d.dmst_store_code,
#              a.dmat_div_code,
#              a.dmat_dep_code,
#              a.dmat_sec_code,
#              a.dmat_grp_code,
#              a.dmat_sgr_code
# ) AS tmp
# JOIN tesco_analysts.hierarchy_spm c
#     ON tmp.dmat_div_code = LPAD(CAST(c.div_code AS STRING), 4, '0')
#     AND tmp.dmat_dep_code = LPAD(CAST(c.dep_code AS STRING), 4, '0')
#     AND tmp.dmat_sec_code = LPAD(CAST(c.sec_code AS STRING), 4, '0')
#     AND tmp.dmat_grp_code = LPAD(CAST(c.grp_code AS STRING), 4, '0')
#     AND tmp.dmat_sgr_code = LPAD(CAST(c.sgr_code AS STRING), 4, '0')
# GROUP BY tmp.cntr_code,
#          CONCAT(YEAR(tmp.orsrec_date), LPAD(WEEKOFYEAR(tmp.orsrec_date), 2, '0')),
#          tmp.site,
#          pmg
    
    
    
    
#     """
        
        
#     b = pd.read_sql(cases_delivered, conn)    
    # unit_cloth = """
    
    
    
    #     SELECT 
    #         CASE 
    #             WHEN a.int_cntr_id = 1 THEN 'CZ'
    #             WHEN a.int_cntr_id = 2 THEN 'SK'
    #             WHEN a.int_cntr_id = 4 THEN 'HU'
    #             ELSE 'Unknown'
    #         END AS country,
    #         CAST(CONCAT(a.int_cntr_id,a.store) AS INT) as store,
    #         SUBSTRING(d.pmg, 1, 3) AS dep,
    #         d.pmg as pmg,
    #         b.slad_tpnb AS TPNB,
    #         b.slad_tpn AS TPN,
    #         b.slad_long_des AS product_name,
    #         SUM(a.qty) AS unit_delivered
    #     FROM stg_go.go_106_order_receiving a
    #     LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product AND b.cntr_id = a.int_cntr_id
    #     JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = LPAD(d.div_code, 4, '0')
    #         AND b.dmat_dep_code = LPAD(d.dep_code, 4, '0')
    #         AND b.dmat_sec_code = LPAD(d.sec_code, 4, '0')
    #         AND b.dmat_grp_code = LPAD(d.grp_code, 4, '0')
    #         AND b.dmat_sgr_code = LPAD(d.sgr_code, 4, '0')
    #     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
    #     WHERE a.int_cntr_id IN (1, 2, 4)
    #         AND a.part_col BETWEEN 20240919 AND 20240919
    #         AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
    #         AND CAST(CONCAT(a.int_cntr_id,a.store) AS INT) = 41520
    #     GROUP BY  
    #         CASE 
    #             WHEN a.int_cntr_id = 1 THEN 'CZ'
    #             WHEN a.int_cntr_id = 2 THEN 'SK'
    #             WHEN a.int_cntr_id = 4 THEN 'HU'
    #             ELSE 'Unknown'
    #         END,
    #         CAST(CONCAT(a.int_cntr_id,a.store) AS INT),
    #         b.slad_tpnb,
    #         b.slad_tpn,
    #         b.slad_long_des,
    #         SUBSTRING(d.pmg, 1, 3),
    #         d.pmg"""    
    
    
    
#     cloth = f"""
# WITH sold_units AS (
#     SELECT
#         mstr.cntr_code AS country,
#         SUBSTRING(d.pmg, 1, 3) AS dep,
#         mstr.slad_tpnb AS TPNB,
#         mstr.slad_tpn AS TPN,
#         mstr.slad_long_des AS product_name,
#         SUM(sunit.slsms_unit) AS sold_units
#     FROM dw.sl_sms sunit 
#     JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
#     JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
#     JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code, 4, '0') 
#         AND mstr.dmat_dep_code = LPAD(d.dep_code, 4, '0') 
#         AND mstr.dmat_sec_code = LPAD(d.sec_code, 4, '0') 
#         AND mstr.dmat_grp_code = LPAD(d.grp_code, 4, '0') 
#         AND mstr.dmat_sgr_code = LPAD(d.sgr_code, 4, '0') 
#     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
#     WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
#         AND sunit.part_col BETWEEN {start} AND {end}
#         AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
#     GROUP BY mstr.cntr_code, mstr.slad_tpnb, mstr.slad_tpn, mstr.slad_long_des, SUBSTRING(d.pmg, 1, 3)
# ),
# delivered_units AS (
#     SELECT 
#         CASE 
#             WHEN a.int_cntr_id = 1 THEN 'CZ'
#             WHEN a.int_cntr_id = 2 THEN 'SK'
#             WHEN a.int_cntr_id = 4 THEN 'HU'
#             ELSE 'Unknown'
#         END AS country,
#         SUBSTRING(d.pmg, 1, 3) AS dep,
#         b.slad_tpnb AS TPNB,
#         b.slad_tpn AS TPN,
#         b.slad_long_des AS product_name,
#         SUM(a.qty) AS unit_delivered
#     FROM stg_go.go_106_order_receiving a
#     LEFT JOIN dm.dim_artgld_details b ON b.slad_tpnb = a.product AND b.cntr_id = a.int_cntr_id
#     JOIN tesco_analysts.hierarchy_spm d ON b.dmat_div_code = LPAD(d.div_code, 4, '0')
#         AND b.dmat_dep_code = LPAD(d.dep_code, 4, '0')
#         AND b.dmat_sec_code = LPAD(d.sec_code, 4, '0')
#         AND b.dmat_grp_code = LPAD(d.grp_code, 4, '0')
#         AND b.dmat_sgr_code = LPAD(d.sgr_code, 4, '0')
#     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = a.part_col
#     WHERE a.int_cntr_id IN (1, 2, 4)
#         AND a.part_col BETWEEN {start} AND {end}
#         AND SUBSTRING(d.pmg, 1, 3) = 'CLG'
#     GROUP BY  
#         CASE 
#             WHEN a.int_cntr_id = 1 THEN 'CZ'
#             WHEN a.int_cntr_id = 2 THEN 'SK'
#             WHEN a.int_cntr_id = 4 THEN 'HU'
#             ELSE 'Unknown'
#         END,
#         b.slad_tpnb,
#         b.slad_tpn,
#         b.slad_long_des,
#         SUBSTRING(d.pmg, 1, 3)
# )
# SELECT 
#     COALESCE(s.country, d.country) AS country,
#     COALESCE(s.dep, d.dep) AS dep,
#     COALESCE(s.TPNB, d.TPNB) AS TPNB,
#     COALESCE(s.TPN, d.TPN) AS TPN,
#     COALESCE(s.product_name, d.product_name) AS product_name,
#     COALESCE(s.sold_units, 0) AS sold_units,
#     COALESCE(d.unit_delivered, 0) AS unit_delivered
# FROM sold_units s
# FULL OUTER JOIN delivered_units d
#     ON s.country = d.country
#     AND s.dep = d.dep
#     AND s.TPNB = d.TPNB
#     AND s.TPN = d.TPN
#     AND s.product_name = d.product_name
# ORDER BY 
#     country,
#     dep
# """
           
#     b = pd.read_sql(cloth, conn)
    
    
    
    
#     sold_unit_diff_tpnb = f"""

#     SELECT
#         mstr.cntr_code AS country,
#         SUBSTRING(d.pmg, 1, 3) AS dep,
#         mstr.slad_tpnb AS TPNB,
#         mstr.slad_tpn AS TPN,
#         mstr.slad_long_des AS product_name,
#         mstr.slad_unit AS unit_type,
#         mstr.slad_case_size AS case_capacity,
#         mstr.slad_net_weight AS weight,
#         SUM(sunit.slsms_unit) AS sold_units
#     FROM dw.sl_sms sunit 
#     JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
#     JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
#     JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code, 4, '0') 
#         AND mstr.dmat_dep_code = LPAD(d.dep_code, 4, '0') 
#         AND mstr.dmat_sec_code = LPAD(d.sec_code, 4, '0') 
#         AND mstr.dmat_grp_code = LPAD(d.grp_code, 4, '0') 
#         AND mstr.dmat_sgr_code = LPAD(d.sgr_code, 4, '0') 
#     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
#     WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
#         AND sunit.part_col BETWEEN {start} AND {end}
#         AND sunit.slsms_unit > 0
#     GROUP BY mstr.cntr_code, mstr.slad_tpnb, mstr.slad_tpn, mstr.slad_long_des,mstr.slad_unit,mstr.slad_case_size,mstr.slad_net_weight,  SUBSTRING(d.pmg, 1, 3)


# """
           
    # b = pd.read_sql(sold_unit_diff_tpnb, conn)
    
    
    
    
    # product_stocked =  f"""
    #     SELECT
    #         mstr.cntr_code AS country,
    #         CAST(stores.dmst_store_code AS INT) AS store,  
    #         cal.dmtm_fw_code AS week,
    #         SUBSTRING(d.pmg, 1, 3) AS dep,
    #         COUNT(DISTINCT mstr.slad_tpn) AS product_stocked
    #     FROM dw.sl_sms sunit 
    #     JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
    #     JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
    #     JOIN tesco_analysts.hierarchy_spm d ON mstr.dmat_div_code = LPAD(d.div_code,4,"0") 
    #         AND mstr.dmat_dep_code = LPAD(d.dep_code,4,"0") 
    #         AND mstr.dmat_sec_code = LPAD(d.sec_code,4,"0") 
    #         AND mstr.dmat_grp_code = LPAD(d.grp_code,4,"0") 
    #         AND mstr.dmat_sgr_code = LPAD(d.sgr_code,4,"0") 
    #     LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    #     WHERE 
    #         sunit.part_col BETWEEN {start} AND {end}
    #         AND CAST(stores.dmst_store_code AS INT) = 41820
    #         AND sunit.slsms_unit > 0
    #     GROUP BY mstr.cntr_code, stores.dmst_store_code, cal.dmtm_fw_code, SUBSTRING(d.pmg, 1, 3)





    # """
    

        
    # b = pd.read_sql(product_stocked, conn)
    
    
    
    
# stores = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl\Repl_Stores_Inputs_2024_Q2_produce_whatif.xlsx")["Store"].unique().tolist()
# formats = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\Repl\Repl_Stores_Inputs_2024_Q2_produce_whatif.xlsx")[["Store", "Format"]].drop_duplicates()
# formats.columns = [x.lower() for x in formats.columns]


# df = b[b["store"].isin(stores)]

# df = df.merge(formats, on=["store"], how="left")


# df.to_parquet(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_09\sold_delivered_unit_VIZ\sold_units_delivered_2024_weeklyFullyear.parquet", compression="gzip")
    


    # check_refit = """
    # SELECT
    #     mstr.cntr_code AS country,
    #     CAST(stores.dmst_store_code AS INT) AS store,  
    #     -- SUM(sunit.slsms_unit) AS sold_units,
    #     COUNT(DISTINCT cal.dmtm_d_code) AS days_with_sales
    # FROM dw.sl_sms sunit 
    # JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id
    # LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
    # JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
    # WHERE stores.cntr_code IN ('SK', 'CZ', 'HU')
    #     AND cal.dmtm_fw_code BETWEEN 'f2023w14' AND 'f2023w27'
    #     AND sunit.slsms_unit > 50
    # GROUP BY mstr.cntr_code, stores.dmst_store_code
    # """

    # b = pd.read_sql(check_refit, conn)
