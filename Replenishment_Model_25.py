import pandas as pd
import Replenishment_Model_Functions_25 as rmf
import Warehouse_Repl_Functions as wrf
import Get_System_Data_SQL as gsd
import ClassesModel as cm
import pyarrow.parquet as pq
import time
import warnings
import numpy as np
from SSH.SSH_volume_functions import ssh_table_create, ssh_downloader
import ad_hoc_py.packaging_type_table_transforming as ob




warnings.filterwarnings("ignore")


@rmf.timeit
def RunSQL_queries(sql_part, data_paths):
    
    
    if sql_part.download_create_dataset_combined == True:
        
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")

        for what_to_create in ['combined_data']: 
        
            sql = ssh_table_create(what_to_create, sql_part.start_date, sql_part.end_date, sql_part.pmg_list, sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
            # if sql ==0:
            #     ssh_downloader(what_to_create, sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)

    

    if sql_part.download_create_dataset == True:
        
        repl_dataset = gsd.Create_Dataset(sql_part, data_paths)
        

    if sql_part.item_sold_sql == True:
        
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")

        for what_to_create in ['item_sold', 'item_sold_dotcom']: #'item_sold',
        
            sql = ssh_table_create(what_to_create, sql_part.start_date, sql_part.end_date, sql_part.pmg_list, sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
            if sql ==0:
                ssh_downloader(what_to_create, sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)


    if sql_part.stock_sql == True:
        
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        
        sql = ssh_table_create("stock", sql_part.start_date, sql_part.end_date, sql_part.pmg_list, sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
        if sql ==0:
            ssh_downloader("stock", sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)


    if sql_part.cases_sql == True:
        
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        
        sql = ssh_table_create("cases", sql_part.start_date, sql_part.end_date, sql_part.pmg_list, sql_part.nr_weeks, sql_part.place_to_save, sql_part.saved_filename)        
        if sql ==0:
            ssh_downloader("cases", sql_part.place_to_save, sql_part.saved_filename, sql_part.stores)

    if sql_part.opsdev_sql == True:
        time_start = time.time()
        
        SRD_tables = gsd.SRD_database_by_stores(sql_part.stores)
        opsdev_df, foil_df = gsd.SRD_to_opsdev(SRD_tables, data_paths.directory, data_paths.excel_inputs_f)
        opsdev_df.to_parquet( f"{sql_part.place_to_save}\{sql_part.saved_filename}\opsdev_df_{sql_part.saved_filename}", compression="gzip")
        foil_df.to_parquet( f"{sql_part.place_to_save}\{sql_part.saved_filename}\Foil_df_{sql_part.saved_filename}", compression="gzip")

        time_stop = time.time()
        print(
            "opsdev_sql - Executed Time: (sec): {:,.2f} ".format(time_stop - time_start)
        )

    if sql_part.planogram_sql == True:
        time_start = time.time()
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        gsd.planogram_compiling(
            sql_part.date_plan, sql_part.saved_filename, sql_part.place_to_save, False
        )
        time_stop = time.time()
        print(
            "planogram_sql - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )

    if sql_part.pallet_capacity == True:
        time_start = time.time()
        gsd.pallet_capacity(
            sql_part.pmg_list, sql_part.saved_filename, sql_part.place_to_save
        )
        time_stop = time.time()
        print(
            "pallet_capacity_CE - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )

    if sql_part.losses_RTC_FoodBank_sql == True:
        
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        time_start = time.time()
        gsd.losses_sql(
            sql_part.start_date,
            sql_part.end_date,
            sql_part.pmg_list,
            sql_part.country,
            sql_part.nr_weeks,
            sql_part.saved_filename,
            sql_part.place_to_save,
            sql_part.stores,
        )
        # gsd.losses_final(sql_part.saved_filename, sql_part.place_to_save)
        time_stop = time.time()
        print(
            "Losses_sql - Executed Time: (sec): {:,.2f} ".format(time_stop - time_start)
        )

    if sql_part.WGLL_gapScan == True:
        time_start = time.time()
        gsd.WGLL_driver_gap_scan(
            sql_part.start_date,
            sql_part.end_date,
            sql_part.nr_weeks,
            sql_part.place_to_save,
            sql_part.saved_filename,
        )
        time_stop = time.time()
        print(
            "WGLL_gapScan - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )

    if sql_part.WH_deliveries == True:
        time_start = time.time()
        gsd.wh_dataset_no_delivery(
            sql_part.start_date,
            sql_part.end_date,
            sql_part.nr_weeks,
            sql_part.place_to_save,
            sql_part.saved_filename,
            data_paths.directory,
            data_paths.excel_inputs_f,
            data_paths.night_truck_helper
        )
        time_stop = time.time()
        print(
            "WH_drivers - Executed Time: (sec): {:,.2f} ".format(time_stop - time_start)
        )

    if sql_part.WH_cases_PMG_level == True:
        time_start = time.time()
        gsd.wh_dataset_no_of_cases_PMG(
            sql_part.start_date,
            sql_part.end_date,
            sql_part.nr_weeks,
            sql_part.place_to_save,
            sql_part.saved_filename,
            data_paths.directory,
            data_paths.excel_inputs_f,
        )
        time_stop = time.time()
        print(
            "WH_cases_PMG - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )

    if sql_part.Rc1_products == True:
        time_start = time.time()
        gsd.Rc1_products_pmg(
            sql_part.nr_weeks,
            sql_part.start_date,
            sql_part.end_date,
            sql_part.saved_filename,
            sql_part.place_to_save,
        )
        time_stop = time.time()
        print(
            "Rc1_products - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )
        
    if sql_part.ob_packaging == True:
        time_start = time.time()
        ob.OB_packaging_types_download(f"{sql_part.place_to_save}\{sql_part.saved_filename}")
        time_stop = time.time()
        print(
            "OB_packaging_types - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )
        
    if sql_part.modul_nr == True:
        time_start = time.time()
        rmf.create_a_folder(data_paths.directory, f"{sql_part.place_to_save}/{sql_part.saved_filename}")
        gsd.Modul_numbers(data_paths.excel_inputs_f,
                          sql_part.saved_filename,
                          sql_part.place_to_save,
                          data_paths.directory)
        time_stop = time.time()
        print(
            "CE_Modul_Numbers - Executed Time: (sec): {:,.2f} ".format(
                time_stop - time_start
            )
        )
    try:   
        return repl_dataset
    except:
        pass
        
        

@rmf.timeit
def Replenishment_Model_Running(
    model_true_false: cm.ModelTrueFalse,
    act_version_name: str,
    version_saved: str,
    data_paths: cm.Data_Paths,
    saved_filename: str,
    for_what_ifs: cm.What_Ifs_Inputs,
):
    

    starttime = rmf.CurrentTime()

    # Model Variables
    RC_CAPACITY = 1 + (1 - 0.62)
    RC_DELIVERY = 0.23
    RC_VS_PAL_CAPACITY = 0.62
    REX_ALLOWANCE = 4
    RC_Capacity_Ratio = 1 + (1 - 0.62)
    shelf_trolley_cap_ratio_to_pallet = 9
    shelf_trolley_cap_ratio_to_rollcage = 5
    backstock_target = 0.4
    MODULE_CRATES = 8
    TABLE_CRATES = 4
    SALES_CYCLE = (0.2, 0.2, 0.2, 0.2, 0.2)
    FULFILL_TARGET = 0.6
    capping_shelves_ratio = 0.075 #0.075
    
    news_mags_rate_for_SK = 1.7216831482098829

    # for Productivity CostBase it is needed
    if model_true_false.INSIGHT_BUSINESS == True:
        rmf.Replenishment_Insight(
            act_version_name, data_paths.directory, data_paths.stores, data_paths.excel_inputs_f
        )
    if model_true_false.Run_CE_Charts == True:
        rmf.plotly_CE_chart_cost_base_and_repl_type_percent(
            act_version_name,
            data_paths.directory,
            data_paths.repl_dataset_f,
            data_paths.stores,
        )

    if for_what_ifs.TPN_Cost_Base == True:
        (
            costbase_tpnb,
            cost_base_dep_grouped,
            cost_base_div_total,
        ) = rmf.cost_base_on_TPNB(
            model_true_false.tpnb_store,
            model_true_false.tpnb_country,
            data_paths.selected_tpn,
            data_paths.repl_dataset_f,
            data_paths.excel_inputs_f,
            data_paths.act_model_cost_base,
            backstock_target,
            data_paths.directory,
            data_paths.stores,
        )

    if for_what_ifs.only_WH == True:

        print("\nOnly Warehouse Replenishment Part is being run.....\n")

        # --- Warehouse Calculation --- #
        store_inputs = rmf.Store_Inputs_Creator(
            data_paths.directory, data_paths.excel_inputs_f, data_paths.stores
        )

        store_inputs_wh, Drivers_WH, sales_wh = wrf.WareHouse_Drivers_Profiles(
            data_paths.directory,
            data_paths.excel_inputs_f,
            data_paths.wh_prof_drivers,
            data_paths.wh_cases_pmg,
            data_paths.wh_pattisserie,
            data_paths.wh_deliveries,
            data_paths.stores,
        )
        wh_times_activities = wrf.WareHouse_TimeValues(
            data_paths.directory,
            data_paths.wh_most,
            store_inputs_wh,
            data_paths.excel_inputs_f,
            Drivers_WH,
            data_paths.stores,
        )
        wh_hours_df = wrf.WareHouse_HoursCalculation(wh_times_activities)

        repl_wh_hours, insight_diff_act_groups, opb, insight, driver_dep, insight_diff = rmf.OperationProductivityBasics_WH(
            data_paths.directory,
            data_paths.act_model_insights,
            wh_hours_df,
            store_inputs,
        )
        hrs_comparison, hrs_comparison_dep = rmf.OutputsComparison(
            data_paths.directory, repl_wh_hours, data_paths.act_model_outputs
        )

        hrs_comparison = hrs_comparison[hrs_comparison["Division"] == "Warehouse"]
        hrs_comparison_dep = hrs_comparison_dep[
            hrs_comparison_dep["Division"] == "Warehouse"
        ]
        
        drivers_dep = None
        
        
    if model_true_false.cases_to_replenish_only:
        
        print("\nOnly Cases to Replenish Part is being run.....\n")
        
        store_inputs = rmf.Store_Inputs_Creator(
            data_paths.directory, data_paths.excel_inputs_f, data_paths.stores
        )
        
        Repl_Dataset = pq.read_table(
            data_paths.directory / data_paths.repl_dataset_f,
            filters=[("store", "in", data_paths.stores)],
        ).to_pandas()
        Repl_Dataset["is_capping_shelf"] = 0
        # Repl_Dataset["single_pick"] = 0
        Repl_Dataset = Repl_Dataset.loc[:, ~Repl_Dataset.columns.isin(['tpn',
                                                                        'division_hier','DIV_ID',
                                                                        'department','DEP_ID',
                                                                        'section','SEC_ID',
                                                                        'group','GRP_ID',
                                                                        'subgroup','SGR_ID', 'supplier_name'])]
        #Repl_Dataset["foil"] = 1
        # Repl_Dataset["sold_units"] = Repl_Dataset['sold_units_dotcom'] + Repl_Dataset["sold_units"]
        # Repl_Dataset = Repl_Dataset[~Repl_Dataset.dep.isin(['SFM','SFB'])]
        # Repl_Dataset.drop("shelfCapacity",axis=1,inplace=True)
        # Repl_Dataset.rename(columns={"capacity":"shelfCapacity"},inplace=True)
        
        cases_to_replenish_df, cases_to_replenish_df_tpnb = rmf.Repl_Drivers_Calculation_TPN(
                                                                                            data_paths.directory,
                                                                                            Repl_Dataset,
                                                                                            store_inputs,
                                                                                            backstock_target,
                                                                                            RC_Capacity_Ratio,
                                                                                            shelf_trolley_cap_ratio_to_pallet,
                                                                                            shelf_trolley_cap_ratio_to_rollcage,
                                                                                            data_paths.excel_inputs_f,
                                                                                            MODULE_CRATES,
                                                                                            TABLE_CRATES,
                                                                                            FULFILL_TARGET,
                                                                                            SALES_CYCLE,
                                                                                            RC_CAPACITY,
                                                                                            RC_DELIVERY,
                                                                                            RC_VS_PAL_CAPACITY,
                                                                                            model_true_false.only_tpn,
                                                                                            model_true_false.tpnb_store,
                                                                                            model_true_false.tpnb_country,
                                                                                            data_paths.selected_tpn,
                                                                                            capping_shelves_ratio,
                                                                                            data_paths.stores,
                                                                                            None,
                                                                                            model_true_false.shelfService_gm,
                                                                                            model_true_false.cases_to_replenish_only
                                                                                        )
        


    if (
        not (
            model_true_false.INSIGHT_BUSINESS
            or model_true_false.Run_CE_Charts
            or for_what_ifs.TPN_Cost_Base
            or for_what_ifs.only_WH
            or model_true_false.cases_to_replenish_only
        )
        == True
    ):

        print("\nReplenishment Model is running....\n")

        # Create a dataframe for excel file with External Drivers/Profiles
        store_inputs = rmf.Store_Inputs_Creator(
            data_paths.directory, data_paths.excel_inputs_f, data_paths.stores
        )

        ###########################################################################
        # Creating Dataset ("without running model, focusing on Dataset table only")
        ###########################################################################
        if model_true_false.Run_Dataset_only == True:

            # Create Dataset which is base for Drivers calculations
                Repl_Dataset = rmf.Repl_DataSet_Creator(
                    store_inputs,
                    data_paths.directory,
                    data_paths.stock_f,
                    data_paths.ops_dev_f,
                    data_paths.items_sold_f,
                    data_paths.items_sold_dotcom,
                    data_paths.cases_f,
                    data_paths.box_op_type_f,
                    data_paths.pallet_capacity_f,
                    model_true_false.broken_case,
                    data_paths.broken_cases_f,
                    data_paths.single_pick_f,
                    data_paths.foil_f,
                    data_paths.shelfCapacity,
                    None,
                    None,
                    None,
                    None)
                

                if model_true_false.broken_case == True:
                    Repl_Dataset = rmf.Broken_Case_Creator(
                        data_paths.directory, Repl_Dataset, 68, 115, 160, version_saved
                    )
                    
                if model_true_false.shelfService_gm:
                    Repl_Dataset = rmf.shelfService_GM(Repl_Dataset)

                file_name = f"inputs/Repl_Dataset_{version_saved}"
                print(f"Repl_Dataset_{version_saved} is being saved.....")
                Repl_Dataset.to_parquet(
                    data_paths.directory / file_name,
                    compression="gzip",
                )

        ###########################################################################
        # Run model with Creating Dataset or existing Dataset from source
        ###########################################################################
        else:
            # Create Dataset which is base for Drivers calculations
            if model_true_false.DATASET_TPN_FUNC == True:
                Repl_Dataset = rmf.Repl_DataSet_Creator(
                    store_inputs,
                    data_paths.directory,
                    data_paths.stock_f,
                    data_paths.ops_dev_f,
                    data_paths.items_sold_f,
                    data_paths.items_sold_dotcom,
                    data_paths.cases_f,
                    data_paths.box_op_type_f,
                    data_paths.pallet_capacity_f,
                    model_true_false.broken_case,
                    data_paths.broken_cases_f,
                    data_paths.single_pick_f,
                    data_paths.foil_f,
                    data_paths.shelfCapacity,
                    None,
                    None,
                    None,
                    None
                    )
                if model_true_false.broken_case == True:
                    Repl_Dataset = rmf.Broken_Case_Creator(
                        data_paths.directory, Repl_Dataset, 68, 115, 160, version_saved
                    )
                    
                if model_true_false.shelfService_gm:
                    Repl_Dataset = rmf.shelfService_GM(Repl_Dataset)

                # file_name = f"inputs/Repl_Dataset_2024_{version_saved}"
                # print(f"Repl_Dataset_2024_{version_saved} is being saved.....")
                # Repl_Dataset.to_pandas().to_parquet(
                #     data_paths.directory / file_name,
                #     compression="gzip",
                # )
                # Repl_Dataset["is_capping_shelf"] = 0
                


            ###########################################################################
            # Department Level Calculation ("Full Calculation")
            ###########################################################################
            if not (model_true_false.tpnb_store or model_true_false.tpnb_country):

                # --- FULL CALCULATION --- #
                ###########################################################################################################
                """
                In this case the model will run the full process on DEPARTMENT level(DRY,BWS,HEA,PPD,FRZ,DAI,PRO,WH,GM,NEW)
                    - Calculated by TPN level but it will be grouped into DEP level in the end
                    - There is always 'AS-IS' xlsx which it is compared to
                """
                ###########################################################################################################

                print('\n\nCalculation mode: "Full Calculation"\n')

                if (
                    not (
                        model_true_false.DATASET_TPN_FUNC
                        or model_true_false.broken_case
                    )
                    == True
                ):
                    # --- Opening Repl_Dataset --- #
                    Repl_Dataset = pq.read_table(
                        data_paths.directory / data_paths.repl_dataset_f,
                        filters=[("store", "in", data_paths.stores)],
                    ).to_pandas()
                    Repl_Dataset["is_capping_shelf"] = 0
                    # Repl_Dataset["single_pick"] = 0
                    # if model_true_false.shelfService_gm:
                    #     Repl_Dataset = rmf.shelfService_GM(Repl_Dataset)
                    
                    
                    
                    # Create a set of store numbers for faster lookup
                    target_stores = {25002,25003,25005,25006,25011,25012,25016,25018,25023,25024,25026,25028,25032,25033,25034}
                    
                    # Create the filter condition
                    # Only exclude HDL where store is in the target set
                    mask = ~((Repl_Dataset['dep'] == 'HDL') & 
                             (Repl_Dataset['store'].isin(target_stores)))
                    
                    Repl_Dataset = Repl_Dataset[mask]
                        
                    Repl_Dataset = Repl_Dataset.loc[:, ~Repl_Dataset.columns.isin([
                                                                                    'division_hier','DIV_ID',
                                                                                    'department','DEP_ID',
                                                                                    'SEC_ID',
                                                                                    'GRP_ID',
                                                                                    'SGR_ID', 'supplier_name', 'weeks','weeksgm'])]
                    
                    
                    # # Get the mask for rows where dep is BWS or DRY
                    # mask = Repl_Dataset.dep.isin(["BWS", "DRY"])
                    
                    # # Process each column in the list
                    # for x in ["srp", "nsrp", "full_pallet", "split_pallet", "mu"]:
                    #     # For filtered rows, set nsrp=1 and others=0
                    #     if x == "nsrp":
                    #         Repl_Dataset.loc[mask, x] = 1
                    #     else:
                    #         Repl_Dataset.loc[mask, x] = 0
                    
                    
                    #Repl_Dataset["foil"] = 1
                    Repl_Dataset = Repl_Dataset[~Repl_Dataset.dep.isin(['SFB'])]
                    
                    presort_pal_del_df = pd.read_parquet(data_paths.directory / data_paths.presort_pal_del)
                    
                    Repl_Dataset = Repl_Dataset.merge(presort_pal_del_df[['country', 'store', 'pmg', 'pre_sort_perc_by_pmg', 'pallet_%',
                                                                          'MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%','PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']].drop_duplicates(),
                                                      on=['country', 'store', 'pmg'], how='left')
                    
                    Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%','MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%',
                    'PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']] = Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%','MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%',
                    'PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']].replace(np.nan,0)
                                                                                   
                    Repl_Dataset.loc[Repl_Dataset['country'] == "SK", "pre_sort_perc_by_pmg"] = 0                                                  
                    # Repl_Dataset["shelfCapacity"] = np.where(Repl_Dataset.division.isin(["Grocery"]), Repl_Dataset["shelfCapacity"] * 1.1, Repl_Dataset["shelfCapacity"] )                                                         
                    
                    # Repl_Dataset = Repl_Dataset[Repl_Dataset.dep.isin(["PRO"])]
                    
                    # Repl_Dataset['pmg'] = np.where(Repl_Dataset['pmg'] == 'SFM03', 'SFP01', Repl_Dataset['pmg'])
                    # Repl_Dataset['dep'] = np.where(Repl_Dataset['pmg'] == 'SFP01', 'SFP', Repl_Dataset['dep'])
                    # Repl_Dataset.drop("shelfCapacity",axis=1,inplace=True)
                    # Repl_Dataset.rename(columns={"capacity":"shelfCapacity"},inplace=True)
  
                    
                    #############################################################################################

                    # Repl_Dataset["single_pick"] = np.where(Repl_Dataset.tpnb == 100432289, 1,0)
                    # jarda_tpnb = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_05\Jarda_tpnb.xlsx")
                    # Repl_Dataset = Repl_Dataset.merge(jarda_tpnb, on=['country', 'tpnb'], how='left').fillna(0)
                    
                    # repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]
                    
                    # for x in repl_types:
                        
                    #     Repl_Dataset.loc[(Repl_Dataset.Jarda_tpnb == 1), x] = (1 if x == 'srp' else 0)
                    
                    # repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]
                    
                    # for x in repl_types:
                        
                    #     Repl_Dataset.loc[(Repl_Dataset.pmg == "PPD02"), x] = (1 if x == 'srp' else 0)
                    
                    
                    # icream_nsrp_tpnb = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\23_06\icream_nsrp_tpnb.xlsx", usecols=['country','tpnb', 'fr_srp'])
                    # Repl_Dataset = Repl_Dataset.merge(icream_nsrp_tpnb, on=['country', 'tpnb'], how='left').fillna(0)
                    # Repl_Dataset['icream_nsrp'] = np.where(Repl_Dataset.fr_srp == 1, 1, 0)
                    
                    # repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]
                    
                    # for x in repl_types:
                        
                    #     Repl_Dataset.loc[(Repl_Dataset.icream_nsrp == 1), x] = 0
                   
              

###########################################################################################################

                    print(
                        "\nRepl_Dataset is loaded into the memory! Drivers calculation has been started....\n"
                    )
                    
                    
                version = "full_calc"
                # --- Drivers Calculation --- #
                Drivers, Drivers_produce = rmf.Repl_Drivers_Calculation_TPN_optimized(
                    data_paths.directory,
                    Repl_Dataset,
                    store_inputs,
                    backstock_target,
                    RC_Capacity_Ratio,
                    shelf_trolley_cap_ratio_to_pallet,
                    shelf_trolley_cap_ratio_to_rollcage,
                    data_paths.excel_inputs_f,
                    MODULE_CRATES,
                    TABLE_CRATES,
                    FULFILL_TARGET,
                    SALES_CYCLE,
                    RC_CAPACITY,
                    RC_DELIVERY,
                    RC_VS_PAL_CAPACITY,
                    model_true_false.only_tpn,
                    model_true_false.tpnb_store,
                    model_true_false.tpnb_country,
                    data_paths.selected_tpn,
                    capping_shelves_ratio,
                    data_paths.stores,
                    version,
                    model_true_false.shelfService_gm,
                    model_true_false.cases_to_replenish_only
                )
                
                # a = Drivers.groupby(["country", "store", 'division_hier','DIV_ID',
                #                     'department','DEP_ID',], observed=True, as_index=False)["Backstock Cases"].sum()
                # a.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\Calcs\2025_04\backstock_cases_CE_div.xlsx")
                # Drivers[Drivers.dep.isin(["DRY", "HEA", "BWS"])].to_parquet(
                #     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\Calcs\2025_02\25_data",
                #     compression="gzip"
                # )


                Rtc_Waste, active_lines = rmf.RTC_Waste_Food_Donation(
                    data_paths.directory, data_paths.losses_f, Repl_Dataset
                )

                # --- TPN to DEP --- #
                drivers_dep, sales_repl, store_pairs, store_dep_dict = rmf.TPN_level_To_Dep_level(
                    data_paths.directory,
                    data_paths.excel_inputs_f,
                    store_inputs,
                    Drivers,
                    Drivers_produce,
                    RC_Capacity_Ratio,
                    backstock_target,
                    active_lines,
                    Rtc_Waste,
                    # data_paths.presort_pal_del
                    data_paths.presort_pal_del,
                    news_mags_rate_for_SK
                    
                )
                
                drivers_dep["Active_Lines"] = np.where((drivers_dep.Dep=="HDL")&(drivers_dep["Store"] == 44012), drivers_dep["Active_Lines"] + 48, drivers_dep["Active_Lines"])
                
                # drivers_dep['RTC Items'] = drivers_dep['Online price changes_item'] + drivers_dep['RTC Items']

                # --- Combining Times, Drivers and calc hours per activity --- #
                df_times, drivers_dep, drivers_div = rmf.TimeValues_Calculation(
                    data_paths.directory, store_inputs, drivers_dep, data_paths.most_f,
                    data_paths.excel_inputs_f, model_true_false.news_HU
                )
                hours_df = rmf.Model_Hours_Calculation(
                    data_paths.directory,
                    data_paths.excel_inputs_f,
                    store_inputs,
                    df_times,
                    REX_ALLOWANCE,
                )

                # --- Warehouse Calculation --- #
                store_inputs_wh, Drivers_WH, sales_wh = wrf.WareHouse_Drivers_Profiles(
                    data_paths.directory,
                    data_paths.excel_inputs_f,
                    data_paths.wh_prof_drivers,
                    data_paths.wh_cases_pmg,
                    data_paths.wh_pattisserie,
                    data_paths.wh_deliveries,
                    data_paths.stores,
                )
                wh_times_activities = wrf.WareHouse_TimeValues(
                    data_paths.directory,
                    data_paths.wh_most,
                    store_inputs_wh,
                    data_paths.excel_inputs_f,
                    Drivers_WH,
                    data_paths.stores,
                )
                wh_hours_df = wrf.WareHouse_HoursCalculation(wh_times_activities)

                # --- Outputs WH & Repl Combined --- #
                (
                    repl_wh_hours,
                    opb_dep,
                    opb_div,
                    insight,
                    insight_diff,
                    insight_diff_act_groups,
                    opb,
                    opb_div_extended,
                    driver_dep
                ) = rmf.OperationProductivityBasics_ON_TPN(
                    data_paths.directory,
                    data_paths.act_model_insights,
                    hours_df,
                    wh_hours_df,
                    drivers_dep,
                    model_true_false.tpnb_store,
                    model_true_false.tpnb_country,
                    None,
                    sales_repl,
                    sales_wh,
                    store_pairs,
                    store_dep_dict
                )

                hrs_comparison, hrs_comparison_dep = rmf.OutputsComparison(
                    data_paths.directory, repl_wh_hours, data_paths.act_model_outputs
                )
                
                
                store_name = store_inputs[["Store", "Store Name"]].drop_duplicates()
                
                hrs_comparison_dep = hrs_comparison_dep.merge(store_name, on="Store", how="left")
                
                new_order = ['Country', 'Store', 'Store Name', 'Format', 'Division', 'Dep', 
                             'Total Weekly Hours', 'Yearly GBP', 'New_Hours', 'New_Yearly GBP', 
                             'diff_hours', 'diff_%', 'diff_in_GBP']
                
                hrs_comparison_dep = hrs_comparison_dep[new_order]
                
                

        ###########################################################################
        # TPN level Calculation   ("Only for selected TPNs")
        ###########################################################################
        if (model_true_false.tpnb_store or model_true_false.tpnb_country) == True:

            # --- TPN CALCULATION --- #
            ###########################################################################################################
            """
            In this case the model will run on TPNB level
                - Calculated by TPN level to stores/country
                - There is always 'AS-IS' variable calculated during the model running, there is no pre-saved xlsx version of it
            """
            ###########################################################################################################

            if model_true_false.tpnb_store == True:

                print('\n\n Calculation mode: "Store and Tpnb" level\n')

                a = pd.DataFrame()

                filter_tpn = pd.read_excel(
                    data_paths.directory / data_paths.selected_tpn, "store_tpnb"
                )
                dict_list = (
                    filter_tpn.groupby("store")["tpnb"]
                    .apply(lambda s: s.tolist())
                    .to_dict()
                )

                # opening the Dataset based on conditions
                data = pq.read_table(
                    data_paths.directory / data_paths.repl_dataset_f,
                    filters=[("store", "in", [k for k in dict_list.keys()])],
                ).to_pandas()
                

                df = pd.DataFrame()
                for k, v in dict_list.items():
                    a = data.loc[(data.store == k) & (data.tpnb.isin(v))]
                    df = pd.concat([df, a])

                del data


                Repl_Dataset = df.copy()
                data_paths.stores = Repl_Dataset.store.unique().tolist()
                repl_type_cond = [
                    Repl_Dataset.srp == 1,
                    Repl_Dataset.nsrp == 1,
                    Repl_Dataset.full_pallet == 1,
                    Repl_Dataset.mu == 1,
                    Repl_Dataset.split_pallet == 1,
                    Repl_Dataset.icream_nsrp == 1,
                    Repl_Dataset.single_pick == 1,
                ]
                repl_type_result = ["srp", "nsrp", "full_pallet", "mu", "split_pallet", 'icream_nsrp', "single_pick"]
                Repl_Dataset["repl_type"] = np.select(
                    repl_type_cond, repl_type_result, 0
                )



                product_details = Repl_Dataset[
                    [
                        "country",
                        "store",
                        "tpnb",
                        "product_name",
                        "day",
                        "sold_units",
                        "repl_type",
                        "opening_type",
                        "pallet_capacity",
                        "stock",
                        "unit",
                        "case_capacity",
                        "shelfCapacity",
                        'as_is_model_contains?'
                    ]
                ].drop_duplicates()

                del a

                Repl_Dataset["is_capping_shelf"] = 0

                print("\nRepl_Dataset is loaded into the memory!\n")

            if model_true_false.tpnb_country == True:

                print('\n\n Calculation mode: "Country and Tpnb" level\n')
                        

                filter_tpn = pd.read_excel(
                    data_paths.directory / data_paths.selected_tpn, "country_tpnb"
                )
                
                
                for c in filter_tpn.country.unique().tolist():
                    
                    number_of_df = 0
                    
                    
                    rounds_var = rmf.count_lists([x for x in rmf.chunk_list(filter_tpn[filter_tpn.country == c].tpnb.unique().tolist(), for_what_ifs.chunk_size)])
                    
                    print("\n##########################################")
                    print(f"There will be {rounds_var} round(s) in calculation of {c}")
                    print("##########################################\n")

                    
                    all_df = pd.DataFrame()
                    
                    for chunk in rmf.chunk_list(filter_tpn[filter_tpn.country == c].tpnb.unique().tolist(), for_what_ifs.chunk_size):
                        
    
                        # a = pd.DataFrame()
    
                        dict_list = (
                            filter_tpn[(filter_tpn.country == c)
                                       & 
                                       (filter_tpn.tpnb.isin(chunk))].groupby("country")["tpnb"]
                            .apply(lambda s: s.tolist())
                            .to_dict()
                        )
                        for k, v in dict_list.items():
                            Repl_Dataset = pq.read_table(
                                data_paths.directory / data_paths.repl_dataset_f,
                                filters=[("tpnb", "in", v), ("country", "=", k)],
                            ).to_pandas()
                            
                        
                        # If the selected tpnb is a PRO then it will be PRO19 because it is handled like a DRY product
                        Repl_Dataset['pmg'] = np.where(Repl_Dataset['dep'] == 'PRO', 'PRO19', Repl_Dataset['pmg'])
                        
                        presort_pal_del_df = pd.read_parquet(data_paths.directory / data_paths.presort_pal_del)
                        
                        Repl_Dataset = Repl_Dataset.merge(presort_pal_del_df[['country', 'store', 'pmg', 'pre_sort_perc_by_pmg', 'pallet_%']].drop_duplicates(),
                                                          on=['country', 'store', 'pmg'], how='left')
                        
                        Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%']] = Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%']].replace(np.nan,0)
                        
                        
                        
                        
                        
                        
                        
                        presort_pal_del_df = pd.read_parquet(data_paths.directory / data_paths.presort_pal_del)
                        
                        Repl_Dataset = Repl_Dataset.merge(presort_pal_del_df[['country', 'store', 'pmg', 
                                                                              'MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%','PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']].drop_duplicates(),
                                                          on=['country', 'store', 'pmg'], how='left')
                        
                        Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%','MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%',
                        'PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']] = Repl_Dataset[['pre_sort_perc_by_pmg', 'pallet_%','MIX_CAGE_%', 'MIX_PALLET_%', 'PBL_CAGE_%',
                        'PBL_PALLET_%', 'PBS_CAGE_%', 'PBS_PALLET_%']].replace(np.nan,0)
                        
                        if Repl_Dataset.shape[0] > 0:
                            
                            Repl_Dataset.rename(columns={"frozen_srp":"icream_nsrp"},inplace=True)
                            data_paths.stores = Repl_Dataset.store.unique().tolist()
                            
                            
                            repl_type_result = ["srp", "nsrp", "full_pallet", "mu", "split_pallet", "icream_nsrp", "single_pick"]
                            Repl_Dataset['repl_type'] = Repl_Dataset.apply(lambda row: ", ".join([f"{col}: {row[col]*100:.0f}%" for col in Repl_Dataset.columns if col in repl_type_result  and row[col] > 0]), axis=1)
    
                            
            
                            if for_what_ifs.volume_modifier == True:

                                Repl_Dataset["volume_percent_store_tpnb"] = Repl_Dataset[
                                    "sold_units"
                                ] / Repl_Dataset.groupby(["country", "tpnb"], observed=True)[
                                    "sold_units"
                                ].transform(
                                    "sum"
                                )
                                # sold units modifier
                                new_sold_items = (
                                    filter_tpn.groupby(["country", "tpnb"])["as_is_volume"]
                                    .apply(lambda s: s.tolist())
                                    .to_dict()
                                )

                                for k, v in new_sold_items.items():
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        ["sold_units_new"],
                                    ] = (v[0] * Repl_Dataset["volume_percent_store_tpnb"])
                                Repl_Dataset["sold_unit_diff_percent"] = (
                                    Repl_Dataset["sold_units_new"] / Repl_Dataset["sold_units"]
                                )
                                Repl_Dataset["sold_unit_diff_percent"] = (
                                    Repl_Dataset["sold_unit_diff_percent"]
                                    .replace(np.nan, 0)
                                    .replace([np.inf, -np.inf], 0)
                                )
                                # Repl_Dataset.drop(['sold_units_new'], axis=1, inplace=True)
                                for k, v in new_sold_items.items():
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        "sold_units",
                                    ] = (v[0] * Repl_Dataset["volume_percent_store_tpnb"])
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        "cases_delivered",
                                    ] *= Repl_Dataset["sold_unit_diff_percent"]
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        "unit",
                                    ] *= Repl_Dataset["sold_unit_diff_percent"]
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        "stock",
                                    ] *= Repl_Dataset["sold_unit_diff_percent"]
                                    Repl_Dataset.loc[
                                        (Repl_Dataset.country == k[0])
                                        & (Repl_Dataset.tpnb == k[1])
                                        & (v[0] >= 0),
                                        "shelfCapacity",
                                    ] *= Repl_Dataset["sold_unit_diff_percent"]

                                Repl_Dataset.drop(
                                    [
                                        "sold_units_new",
                                        "sold_unit_diff_percent",
                                        "volume_percent_store_tpnb",
                                    ],
                                    axis=1,
                                    inplace=True,
                                )

            
                            product_details = Repl_Dataset[
                                [
                                    "country",
                                    "store",
                                    "tpnb",
                                    "product_name",
                                    "day",
                                    "sold_units",
                                    "repl_type",
                                    "opening_type",
                                    "pallet_capacity",
                                    "stock",
                                    "unit",
                                    "case_capacity",
                                    "shelfCapacity",
                                    'as_is_model_contains?'
                                ]
                            ].drop_duplicates()
            
                            # del a
            
                            Repl_Dataset["is_capping_shelf"] = 0
                            
                            print("\nRepl_Dataset is loaded into the memory!\n")
                            
                            
                            
                            # if model_true_false.change_repl_type == True:
                
                            version = "as_is"
            
                            as_is = pd.DataFrame()
                            
                            

                            
                            
                            Drivers, Drivers_produce = rmf.Repl_Drivers_Calculation_TPN(
                                data_paths.directory,
                                Repl_Dataset,
                                store_inputs,
                                backstock_target,
                                RC_Capacity_Ratio,
                                shelf_trolley_cap_ratio_to_pallet,
                                shelf_trolley_cap_ratio_to_rollcage,
                                data_paths.excel_inputs_f,
                                MODULE_CRATES,
                                TABLE_CRATES,
                                FULFILL_TARGET,
                                SALES_CYCLE,
                                RC_CAPACITY,
                                RC_DELIVERY,
                                RC_VS_PAL_CAPACITY,
                                model_true_false.only_tpn,
                                model_true_false.tpnb_store,
                                model_true_false.tpnb_country,
                                data_paths.selected_tpn,
                                capping_shelves_ratio,
                                data_paths.stores,
                                version,
                                True,
                                False
                                
                                

                            )
                            
                            cases_to_replenish = Drivers.groupby(['store', 'tpnb'],observed=True)['cases_to_replenish'].sum().reset_index()
            
                            # Combining Times, Drivers and calc hours per activity
                            (
                                df_times,
                                Final_drivers_for_TPN_level,
                            ) = rmf.TimeValues_Calculation_TPN_polars(
                                data_paths.directory,
                                store_inputs,
                                Drivers_produce,
                                Drivers,
                                data_paths.most_f,
                            )
                            hours_df = rmf.Model_Hours_Calculation_TPN_polars(
                                data_paths.directory,
                                data_paths.excel_inputs_f,
                                store_inputs,
                                df_times,
                                REX_ALLOWANCE,
                                version
                            )
                            
                            # hours_df.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_05\a.xlsx", index=False)
                            
            
                            # hrs_comparison, hrs_comparison_dep = rmf.OutputsComparison(data_paths.directory,hours_df,data_paths.act_model_outputs)
                            output, activity_groups = rmf.OperationProductivityBasics_ON_TPN(
                                data_paths.directory,
                                data_paths.act_model_insights,
                                hours_df,
                                None,
                                Final_drivers_for_TPN_level,
                                model_true_false.tpnb_store,
                                model_true_false.tpnb_country,
                                product_details,
                                None,
                                None,
                                None,
                                None
                            )
                            as_is = pd.concat([as_is, output])
                            # hours_df_as_is = hours_df.copy()
                            
                            print("\n###########")
                            print(f"'AS Is' Done! ({c} Part: {number_of_df +1 }/{rounds_var})")
                            print("###########\n")
                            
                            
                            version = "to_be"
                            
                            # all_df = pd.DataFrame()
                            
                            # for x  in ["full_pallet", "nsrp", "srp", "split_pallet", "mu"] : #
                                
                                
                                
                            to_be = pd.DataFrame()

                        
                            Repl_Dataset, product_details = rmf.whatifs_app_settings(
                                model_true_false.tpnb_store,
                                model_true_false.tpnb_country,
                                for_what_ifs.case_cap_modifier,
                                filter_tpn,
                                Repl_Dataset,
                                for_what_ifs.shelf_capacity_modifier,
                                for_what_ifs.volume_modifier,
                                for_what_ifs.sheet_name,
                                for_what_ifs.opening_type,
                                for_what_ifs.repl_type,


                            )
                            
                            
            
                            Drivers, Drivers_produce = rmf.Repl_Drivers_Calculation_TPN(
                                data_paths.directory,
                                Repl_Dataset,
                                store_inputs,
                                backstock_target,
                                RC_Capacity_Ratio,
                                shelf_trolley_cap_ratio_to_pallet,
                                shelf_trolley_cap_ratio_to_rollcage,
                                data_paths.excel_inputs_f,
                                MODULE_CRATES,
                                TABLE_CRATES,
                                FULFILL_TARGET,
                                SALES_CYCLE,
                                RC_CAPACITY,
                                RC_DELIVERY,
                                RC_VS_PAL_CAPACITY,
                                model_true_false.only_tpn,
                                model_true_false.tpnb_store,
                                model_true_false.tpnb_country,
                                data_paths.selected_tpn,
                                capping_shelves_ratio,
                                data_paths.stores,
                                version,
                                True,
                                False
                            )
            
                            # Combining Times, Drivers and calc hours per activity
                            (
                                df_times,
                                Final_drivers_for_TPN_level,
                            ) = rmf.TimeValues_Calculation_TPN_polars(
                                data_paths.directory,
                                store_inputs,
                                Drivers_produce,
                                Drivers,
                                data_paths.most_f,
                            )
                            hours_df = rmf.Model_Hours_Calculation_TPN_polars(
                                data_paths.directory,
                                data_paths.excel_inputs_f,
                                store_inputs,
                                df_times,
                                REX_ALLOWANCE,
                                version
                            )
                            
                            # hours_df.to_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\others\Calculations\2024_05\b.xlsx", index=False)                
            
                            # hrs_comparison, hrs_comparison_dep = rmf.OutputsComparison(data_paths.directory,hours_df,data_paths.act_model_outputs)
                            output, activity_groups = rmf.OperationProductivityBasics_ON_TPN(
                                data_paths.directory,
                                data_paths.act_model_insights,
                                hours_df,
                                None,
                                Final_drivers_for_TPN_level,
                                model_true_false.tpnb_store,
                                model_true_false.tpnb_country,
                                product_details,
                                None,
                                None,
                                None,
                                None
                            )
                            to_be = pd.concat([to_be, output])
                            
                            print("\n###########")
                            print(f"'To Be' Done! ({c} Part: {number_of_df +1 }/{rounds_var})")
                            print("###########")
            
                            to_be.columns = (
                                to_be.iloc[:, :8].columns.tolist()
                                + to_be.iloc[:, 8:].add_suffix("_new").columns.tolist()
                            )
                            diff_table = as_is.merge(
                                to_be,
                                on=[
                                    "Country",
                                    "Store",
                                    "Format",
                                    "Pmg",
                                    "Dep",
                                    "Division",
                                    "Tpnb",
                                    "Product_name",
                                ],
                                how="left",
                            )
                            diff_table["Total_Diff_weekly_Hours"] = (
                                diff_table["Total Weekly Hours_new"]
                                - diff_table["Total Weekly Hours"]
                            )
                            diff_table["Total_Diff_Yearly_GBP"] = (
                                diff_table["Yearly GBP_new"] - diff_table["Yearly GBP"]
                            )
                            diff_table = diff_table.merge(product_details[['Store','Tpnb', 'As_is_model_contains?']].drop_duplicates(), on=['Store','Tpnb'], how='left')
                            
                            
                            
                            
                            # diff_table = diff_table.replace([np.inf, -np.inf], np.nan).groupby(['Country', 'Tpnb', 'Product_name', 'Opening_type', 'Opening_type_new'],
                            #                                 observed=True, as_index=False)[['Total Weekly Hours', 'Yearly GBP', 'Total Weekly Hours_new', 'Yearly GBP_new']].sum()
                            # diff_table['DIFF_in_Weekly_Hours'] = diff_table['Total Weekly Hours_new'] - diff_table['Total Weekly Hours']
                            # diff_table['DIFF_in_Yearly_GBP'] = diff_table['Yearly GBP_new'] - diff_table['Yearly GBP']
                            # # diff_table.drop(['Total Weekly Hours_new', 'Total Weekly Hours', 'Yearly GBP_new', 'Yearly GBP'], axis=1, inplace=True)
                            
                            all_df = pd.concat([all_df, diff_table], axis=0)
                                
                                
                                
                            # Saving diff_table
                            # rmf.DiffOutputFormatter_TPN(
                            #     data_paths.directory,
                            #     f"outputs/ReplTypeChanger_outputs/ReplTypeChanger_output_{version_saved}_{c}.xlsx",
                            #     diff_table,
                            #     activity_groups,
                            #     number_of_df,
                            #     cases_to_replenish
                            # )
                                
                            number_of_df += 1
                            
                            # all_df = pd.concat([all_df, as_is])



                        # rmf.DiffOutputFormatter_TPN_with_openpyxl(
                        #     data_paths.directory,
                        #     f"outputs/ReplTypeChanger_outputs/ReplTypeChanger_output_{version_saved}_{c}.xlsx")
                        
                        

                        # all_df.to_excel(fr"{data_paths.directory}\outputs\ReplTypeChanger_outputs\{c}_Repl_Types_Changer_{version_saved}.xlsx", index=False)
 
                    # else:
                    #     print("\n\n########################################################")
                    #     print("NOTE that some TPNB cannot be found in the Dataset......")
                    #     print("########################################################\n\n")
                    #     pass
                        # new_sold = pd.read_excel(r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023\others\Single_Pick_GM\base_tpnb.xlsx")    

                    # all_df.to_excel(fr"{data_paths.directory}\outputs\ReplTypeChanger_outputs\{c}_Repl_Types_Changer_{version_saved}.xlsx", index=False)



    ###########################################################################
    # Saving Tables from the model and creating summaries
    ###########################################################################
    if not (model_true_false.tpnb_store or model_true_false.tpnb_country) == True:

        # Outputs saving
        if model_true_false.OPB_DEP_SAVE == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/OPB_DEP_{version_saved}.xlsx"
            rmf.OPB_DEP_DIV_formatter(
                opb, data_paths.directory, version_saved, True, driver_dep, None
            )
        if model_true_false.OPB_DIV_SAVE == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/OPB_DIV_{version_saved}.xlsx"
            rmf.OPB_DEP_DIV_formatter(
                opb_div_extended, data_paths.directory, version_saved, False, drivers_div, driver_dep
            )
        if model_true_false.INSIGHT_SAVE == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/INSIGHT_{version_saved}.parquet.gz"
            insight.to_parquet(
                data_paths.directory / file_name, index=False, compression="gzip"
            )
        if model_true_false.INSIGHT_DIFF == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/INSIGHT_DIFF_{version_saved}.parquet.gz"
            insight_diff.to_parquet(
                data_paths.directory / file_name, index=False, compression="gzip"
            )
        if model_true_false.OPB_DEP_DIFF_SAVE == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/OPB_DEP_DIFF_{version_saved}.xlsx"
            rmf.DiffOutputFormatter(data_paths.directory, file_name, hrs_comparison_dep, insight_diff_act_groups)
            if for_what_ifs.only_WH == True:
                pass
        if model_true_false.calc_sheet_insight == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/calc_sheet_insight_{version_saved}.csv.gz"
            hours_df.to_csv(data_paths.directory / file_name, index=False, compression="gzip")
        if model_true_false.Cost_Base == True:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/ReplCostBase_{version_saved}"
            rmf.optimize_objects(rmf.optimize_types(hours_df)).to_parquet(
                data_paths.directory / file_name, compression="gzip"
            )
        if model_true_false.cases_to_replenish_only:
            rmf.create_a_folder(data_paths.directory, f"outputs/model_outputs/{version_saved}")
            file_name = f"outputs/model_outputs/{version_saved}/cases_to_replenish_{version_saved}.xlsx"
            cases_to_replenish_df.to_excel(data_paths.directory / file_name,index=False)
            
            file_name = f"outputs/model_outputs/{version_saved}/cases_to_replenish_{version_saved}_tpn"
            cases_to_replenish_df_tpnb.to_parquet(data_paths.directory / file_name, compression="gzip")
            print("\nCases to Replenish Part is done!!\n")
            
            
            
    if for_what_ifs.TPN_Cost_Base == True:
        file_name = (
            f"outputs/ReplTypeChanger_outputs/CostBase_TPNB_{version_saved}.xlsx"
        )
        rmf.CostBaseFormatter(
            data_paths.directory,
            file_name,
            costbase_tpnb,
            cost_base_dep_grouped,
            data_paths.excel_inputs_f,
            cost_base_div_total,
        )
        # costbase_tpnb.to_excel(data_paths.directory / file_name, index=False)

    endtime = rmf.CurrentTime()
    try:
        summary = cm.ModelSummary(
            starttime, endtime, hrs_comparison, insight_diff_act_groups, repl_wh_hours
        )
        if for_what_ifs.only_WH == True: 
            return summary, insight_diff
        else:
            return summary, insight_diff
    except:
        return None, all_df
