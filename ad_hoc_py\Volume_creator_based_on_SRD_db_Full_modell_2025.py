import sys
import os
import pandas as pd
from pathlib import Path
import numpy as np
import pyodbc
sys.path.append(os.path.dirname(Path.cwd()))
import Get_System_Data_SQL as gsd
from datetime import datetime
import Replenishment_Model_Functions_25 as rmf
import polars as pl
import re
import json
import paramiko
import time
from datetime import datetime
from tqdm import tqdm
import colorama
from colorama import Fore, Back, Style
import csv




try:
    # Try to use __file__ to get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
except NameError:
    # Fallback to using the current working directory
    script_dir = os.getcwd()

# Construct the path to the config file one level up from the script directory or current working directory
config_path = os.path.join(script_dir, os.pardir, 'config.json')

# Load the configuration from the JSON file
with open(config_path, 'r') as file:
    config = json.load(file)

# Extract the necessary details from the configuration
hostname = config['SSH_hostname']
username = config['SSH_username']
password = config['SSH_password']


ODBC_CONN = config['ODBC_connection']




directory = (
    Path(__file__).parent if "__file__" in locals() else Path.cwd()
).parent.parent


# directory = r"C:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2023"
srd_tables_places = r"inputs/JDA_SRD_Tables/"

excel_inputs_f = "inputs/Repl/Repl_Stores_Inputs_2025_Q1_v7_GBPratesUpdate (1)_Zamky&others.xlsx"

stores = tuple(pl.read_excel(directory / excel_inputs_f, engine="calamine").filter(pl.col("Country").is_in(['SK', 'HU', 'CZ']))["Store"].unique())


act_dataset = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\files_for_dataset\plano_0616\Dataset_based_on_plano_0616"

# year = 2025

# period = 1

old_f = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\##WebApps\top_nsrp_dashboard\data\CE_JDA_SRD_for_streamlit_25p4"

start_date = "'f2025w16'"
end_date = "'f2025w20'"
nr_weeks = int(end_date[7:9]) - int(start_date[7:9]) + 1


period = '25p5'

category_reset_df = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\others\CategoryReset\Category Reset CE_hier.xlsx"

folder_name = datetime.now().strftime("%d-%m-%Y")

# modelDataSet_as_is = pd.read_parquet(
#     r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2024\inputs\as_is_modelDataSet_updated_10-01_sServiceUpdALBI_"
#     )

# modelDataSet_as_is.rename(columns={'frozen_srp':'icream_nsrp'},inplace=True)

saved_filename = "db"

def JDA_SRD_CE_report():
    
    global saved_filename

    print("\nJDA_SRD_CE_report has been started....\n")


    if not os.path.isdir(directory / srd_tables_places / f"{folder_name}"):

        os.makedirs(directory / srd_tables_places / f"{folder_name}")
        
    place_to_save = directory / srd_tables_places / f"{folder_name}"
    

        
    def SRD_db():
        
        
        def ssh_table_create(what_to_create, start, end, pmg, nr_weeks, wp_working_output, saved_name):
            
            if what_to_create == "srd":
                
                success = run_srd_script(hostname, password)
                
                if success:
                    flag = 0
                    print("=" * 50)
                    print("🎉 All done! Your SRD table is ready.")
                else:
                    print("💥 Something went wrong. Check the server logs.")
                    print("=" * 50)
                
            else:
            
                # Setup the connection
                try:
                    ssh_client = paramiko.SSHClient()
                    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    ssh_client.connect(hostname=hostname, username="phrubos", password=password)
                    print("\nConnection is done\n")
                except:
                    print("\nNo connection!\n")  
                
                # Setup FTP client
                ftp_client = ssh_client.open_sftp()      
                    
                ftp_client.get(f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql", wp_working_output / saved_name / f"{what_to_create}_create_table.sql")
                
                file_path = wp_working_output / saved_name /  f"{what_to_create}_create_table.sql"
                
                start = start.strip("'")
                end = end.strip("'")
                
                # parameter the SQL file
                with open(file_path, 'r') as file:
                    sql_content = file.read()
            
                    start_pattern = r"(?<=BETWEEN\s)'f(\d{4}w\d{2})'"
                    end_pattern = r"(?<=AND\s)'f(\d{4}w\d{2})'"
                    
                    modified_content = re.sub(start_pattern, f"'{start}'", sql_content)
                    modified_content = re.sub(end_pattern, f"'{end}'", modified_content)
                    
                    
                    # weeks_pattern = r"/\d+\sAS"
            
                    # modified_content = re.sub(weeks_pattern, f"/{nr_weeks} AS", modified_content)
                    
                    if pmg != None:
                    
                        # New pattern to replace PMG values
                        pmg_pattern = r"WHERE SUBSTRING\(pmg, 1, 3\) IN \([^)]+\)"
                        modified_content = re.sub(pmg_pattern, f"WHERE SUBSTRING(pmg, 1, 3) IN {pmg}", modified_content)
                    
            
            
                with open(file_path, 'w') as file:
                    file.write(modified_content)
                    
                    
                ftp_client.put(wp_working_output / saved_name / f"{what_to_create}_create_table.sql", f"/home/<USER>/{what_to_create}/{what_to_create}_create_table.sql")
                
            
                
                print(f"\nSQL file Modification complete. Saved to\n{file_path}\n", )
                    
                    
                print(f"\nScript ({what_to_create}) is being started.....\n")
                    
                stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{what_to_create}/start_q")
                
                exit_status = stdout.channel.recv_exit_status()
                
                flag = 0
                if exit_status == 0:
                    print(f"\nScript ({what_to_create}) finished successfully.\n")
                else:
                    print(f"Script failed with an error:\n{stderr.read().decode('utf-8')}")
                    flag+=1
                    
                ssh_client.close()
            
            return flag

        def run_srd_script(hostname, password, timeout_minutes=90):
            """
            Simple SSH monitor for SRD SQL script execution
            """
            
            print("=" * 50)
            print("🏪 SRD Planogram Analysis Script Monitor")
            print("=" * 50)
            
            
            
            print(f"🔌 Connecting to {hostname}...")
            
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            try:
                # Connect
                ssh_client.connect(hostname=hostname, username="phrubos", password=password, timeout=30)
                print("✅ Connected successfully")
                
                # Quick file check
                stdin, stdout, stderr = ssh_client.exec_command("ls -la /home/<USER>/srd/srd.sql")
                if 'srd.sql' not in stdout.read().decode('utf-8'):
                    print("❌ SQL file not found!")
                    return False
                
                # Start execution
                print(f"🚀 Starting SRD script execution...")
                start_time = time.time()
                
                stdin, stdout, stderr = ssh_client.exec_command("sh /home/<USER>/srd/start_q 2>&1", get_pty=True)
                
                # Monitor progress
                last_update = time.time()
                while True:
                    if stdout.channel.exit_status_ready():
                        break
                        
                    if stdout.channel.recv_ready():
                        chunk = stdout.channel.recv(1024).decode('utf-8', errors='ignore')
                        if chunk:
                            last_update = time.time()
                            
                            # Show only important progress
                            for line in chunk.split('\n'):
                                line = line.strip()
                                if not line:
                                    continue
                                    
                                # Key progress indicators
                                if 'CREATE TABLE' in line.upper():
                                    print("📋 Creating table...")
                                elif 'DROP TABLE' in line.upper():
                                    print("🗑️  Dropping old table...")
                                elif 'SELECT DISTINCT' in line.upper():
                                    print("🔍 Processing data...")
                                elif 'Job' in line and 'finished' in line:
                                    print("✨ Stage completed")
                                elif 'Exception' in line or 'ERROR' in line.upper():
                                    print(f"⚠️  Warning: {line[:80]}...")
                    
                    # Timeout check
                    elapsed = time.time() - start_time
                    idle_time = time.time() - last_update
                    
                    if elapsed > (timeout_minutes * 60):
                        print(f"⏰ Timeout after {timeout_minutes} minutes")
                        return False
                        
                    if idle_time > 60:  # Show progress every minute of silence
                        print(f"⏳ Still running... ({elapsed/60:.1f} minutes elapsed)")
                        last_update = time.time()
                        
                    time.sleep(2)
                
                # Check result
                exit_status = stdout.channel.recv_exit_status()
                total_time = time.time() - start_time
                
                print(f"⏱️  Execution time: {total_time/60:.1f} minutes")
                
                if exit_status == 0:
                    print("✅ Script completed successfully!")
                    
                    # Quick verification
                    print("🔍 Verifying table creation...")
                    cmd = "echo 'SELECT COUNT(*) FROM sch_analysts.tbl_srd_planogram_analysis;' | /opt/spark_thrift_kyuubi/bin/beeline -n phrubos -w /home/<USER>/password_file -u '******************************************************/' --silent=true"
                    
                    stdin, stdout, stderr = ssh_client.exec_command(cmd, timeout=60)
                    output = stdout.read().decode('utf-8')
                    
                    # Extract row count (look for numbers in the output)
                    import re
                    numbers = re.findall(r'\b\d{6,}\b', output)  # Look for 6+ digit numbers
                    if numbers:
                        row_count = int(numbers[-1])  # Take the last large number found
                        print(f"📊 Table created with {row_count:,} rows")
                    else:
                        print("✅ Table created successfully!")
                        
                    return True
                else:
                    print(f"❌ Script failed with exit code: {exit_status}")
                    return False
                    
            except paramiko.AuthenticationException:
                print("❌ Authentication failed - check credentials")
                return False
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                return False
            finally:
                ssh_client.close()
                print("🔌 Connection closed")

        def ssh_downloader(what_to_download: str, wp_working_output: str, saved_name: str, stores: tuple):
        # Initialize colorama for colored output
            colorama.init()
            
            def print_banner():
                """Print a fancy banner"""
                print(f"""
            {Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
            ║                    🚀 SSH DOWNLOADER 2.0 🚀                 ║
            ║              Enhanced with Progress & Style                  ║
            ╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
            """)
            
            def print_status(message, status_type="info"):
                """Print colored status messages"""
                timestamp = datetime.now().strftime("%H:%M:%S")
                
                if status_type == "success":
                    print(f"{Fore.GREEN}✅ [{timestamp}] {message}{Style.RESET_ALL}")
                elif status_type == "error":
                    print(f"{Fore.RED}❌ [{timestamp}] {message}{Style.RESET_ALL}")
                elif status_type == "warning":
                    print(f"{Fore.YELLOW}⚠️  [{timestamp}] {message}{Style.RESET_ALL}")
                elif status_type == "info":
                    print(f"{Fore.CYAN}ℹ️  [{timestamp}] {message}{Style.RESET_ALL}")
                elif status_type == "processing":
                    print(f"{Fore.MAGENTA}⚙️  [{timestamp}] {message}{Style.RESET_ALL}")


            
            print_banner()
            start_time = time.time()
            
            only_store_s = False
            
            if len(stores) < 10:
                only_store_s = True
                print_status(f"Mode: Store-specific ({len(stores)} stores)", "info")
            else:
                print_status(f"Mode: All groups ({len(stores)} stores)", "info")
                
            folder_name = what_to_download
                
            # =============================================================================
            # Paths
            # =============================================================================
            
            output_folder = f"/home/<USER>/{folder_name}/output/"
            main_folder = f"/home/<USER>/{folder_name}/"
            
            print_status(f"Target: {what_to_download}", "info")
            print_status(f"Output folder: {output_folder}", "info")
            
            def download_part(countries: list, only_store_s: bool, stores):    
                # =============================================================================
                # Download files
                # =============================================================================
                print_status("Establishing SSH connection...", "processing")
                
                try:
                    ssh_client = paramiko.SSHClient()
                    ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                    ssh_client.connect(hostname=hostname, username='phrubos', password=password)
                    print_status("SSH Connection established successfully!", "success")
                except Exception as e:
                    print_status(f"SSH Connection failed: {str(e)}", "error")
                    return
                    
                ftp_client = ssh_client.open_sftp()
                
                if what_to_download == 'losses':
                    print_status("Uploading losses_download.sql...", "processing")
                    ftp_client.put(wp_working_output / "losses_download.sql", main_folder + "losses_download.sql")
                    print_status("SQL file uploaded successfully!", "success")

                for country in countries:
                    
                    print(f"\n{Fore.YELLOW}{'='*60}")
                    print(f"🌍 PROCESSING COUNTRY: {country}")
                    print(f"{'='*60}{Style.RESET_ALL}")
                    
                    if not only_store_s:
                        print_status(f"Downloading parameters for {country} (all groups)", "processing")
                        
                        ftp_client.get(main_folder + f"parameters_all_groups_{country}.csv", wp_working_output / f"parameters_all_groups_{country}.csv")
                        time.sleep(2)
            
                        csv_id = pd.read_csv(wp_working_output / f"parameters_all_groups_{country}.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                        
                        if what_to_download in ['srd','losses', 'item_sold_dotcom', 'item_sold', 'cases', 'stock']:
                            csv_id = csv_id-1
                            
                        print_status(f"Found {csv_id} processing rounds for {country}!", "success")
                        
                        print_status("Cleaning remote output directory...", "processing")
                        del_files = f'rm -f {main_folder}output/*'
                        stdin, stdout, stderr = ssh_client.exec_command(del_files)
                        
                        print_status(f"Starting remote processing for {country}...", "processing")
                        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}")    
                            
                    if only_store_s:  
                        print_status(f"Processing specific stores for {country}", "processing")
                        
                        selected_stores_set = set(map(str, stores))
                        
                        ftp_client.get(f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv", wp_working_output / f"parameters_all_groups_{country}_store_spec.csv")
                        
                        file_path = wp_working_output / f"parameters_all_groups_{country}_store_spec.csv"
                        
                        # Categorize filtered stores based on the starting digit
                        cz_stores = sorted(store for store in selected_stores_set if store.startswith('1') and country == "CZ")
                        sk_stores = sorted(store for store in selected_stores_set if store.startswith('2') and country == "SK")
                        hu_stores = sorted(store for store in selected_stores_set if store.startswith('4') and country == "HU")
                        
                        print_status(f"Filtered stores - CZ: {len(cz_stores)}, SK: {len(sk_stores)}, HU: {len(hu_stores)}", "info")
                        
                        # Write the filtered stores to the CSV file
                        with open(file_path, mode='w', newline='') as file:
                            writer = csv.writer(file, delimiter='|')
                            if cz_stores:
                                writer.writerow(['CZ', '1', ','.join(cz_stores)])
                            if sk_stores:
                                writer.writerow(['SK', '1', ','.join(sk_stores)])
                            if hu_stores:
                                writer.writerow(['HU', '1', ','.join(hu_stores)])
                        
                        ftp_client.put(wp_working_output / f"parameters_all_groups_{country}_store_spec.csv", f"/home/<USER>/{folder_name}/parameters_all_groups_{country}_store_spec.csv")
                        
                        ftp_client.get(main_folder + f"parameters_all_groups_{country}_store_spec.csv", wp_working_output / f"parameters_all_groups_{country}_store_spec.csv")
                        time.sleep(2)
            
                        csv_id = pd.read_csv(wp_working_output / f"parameters_all_groups_{country}_store_spec.csv", sep="|", names=['country','id', 'stores'])['id'].count()
                            
                        print_status(f"Found {csv_id} processing rounds for {country}!", "success")
                        
                        print_status("Cleaning remote output directory...", "processing")
                        del_files = f'rm -f {main_folder}output/*'
                        stdin, stdout, stderr = ssh_client.exec_command(del_files)
                        
                        print_status(f"Starting remote processing for {country} (store-specific)...", "processing")
                        stdin, stdout, stderr = ssh_client.exec_command(f"sh /home/<USER>/{folder_name}/start_{country}_store_spec")

                    print_status("Waiting for remote processing to initialize...", "processing")
                    time.sleep(5)
                    
                    # Enhanced download loop with progress bar
                    print(f"\n{Fore.GREEN}📥 DOWNLOADING FILES FOR {country}{Style.RESET_ALL}")
                    
                    counter = 0
                    downloaded_files = []
                    
                    # Create progress bar
                    pbar = tqdm(total=csv_id, desc=f"🔄 {country} Progress", 
                               bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]",
                               colour='green')
                    
                    while counter <= csv_id:
                        try:
                            file_list = ftp_client.listdir(output_folder)
                            
                            try:
                                output = [i for i in file_list if i.__contains__(".zip")][0]
                            except:
                                output = []
                                pass
                            
                            if output and output.__contains__("zip"):
                                
                                # Download the file
                                local_file_path = wp_working_output / output
                                ftp_client.get(output_folder + output, local_file_path)
                                
                                # Get file size for display
                                file_size = os.path.getsize(local_file_path)
                                size_mb = file_size / (1024 * 1024)
                                
                                downloaded_files.append(output)
                                counter += 1
                                
                                # Update progress bar
                                pbar.update(1)
                                pbar.set_postfix({"Last file": output[:30] + "..." if len(output) > 30 else output, 
                                                 "Size": f"{size_mb:.1f}MB"})
                                
                                print_status(f"Downloaded: {output} ({size_mb:.1f}MB)", "success")
                                
                                # Clean up remote file
                                if only_store_s:
                                    time.sleep(2)
                                else:   
                                    time.sleep(5)
                                ftp_client.remove(output_folder + output)
                                
                            else:
                                # Show waiting status
                                pbar.set_postfix({"Status": "Waiting for files...", "Files ready": len(file_list)})
                                
                                if only_store_s:
                                    time.sleep(2)
                                else:
                                    time.sleep(10)
                            
                            if counter == csv_id:
                                break
                                
                        except Exception as e:
                            print_status(f"Error during download: {str(e)}", "error")
                            if only_store_s:
                                time.sleep(2)
                            else:
                                time.sleep(5)
                    
                    pbar.close()
                    print_status(f"Completed downloading {len(downloaded_files)} files for {country}!", "success")
                    
                ftp_client.close()
                ssh_client.close()
                print_status("SSH connection closed", "info")
                
            def map_tuple_to_countries(input_tuple):
                country_codes = {
                    '1': 'CZ',
                    '2': 'SK',
                    '4': 'HU'
                }
                
                result = []
                for number in input_tuple:
                    starting_digit = str(number)[0]
                    if starting_digit in country_codes:
                        country = country_codes[starting_digit]
                        if country not in result:
                            result.append(country)
                
                return result
            
            countries = map_tuple_to_countries(stores)
            print_status(f"Countries to process: {', '.join(countries)}", "info")
            
            # Main download process
            download_part(countries, only_store_s, stores)
            
            print(f"\n{Fore.CYAN}🔍 CHECKING FOR FAILED DOWNLOADS...{Style.RESET_ALL}")
            
            check_to_redownload = []
            found_elements = set()
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                check_to_redownload.clear()
                found_elements.clear()
            
                print_status("Scanning downloaded files for issues...", "processing")
                
                for root, dirs, files in os.walk(wp_working_output):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if file.endswith('.zip') and os.path.getsize(file_path) < 400 and "dotcom" not in file:
                            check_to_redownload.append(file)
                            file_size = os.path.getsize(file_path)
                            print_status(f"Found problematic file: {file} ({file_size} bytes)", "warning")

                for element in check_to_redownload:
                    for sub_element in ['HU', 'SK', 'CZ']:
                        if sub_element in element:
                            found_elements.add(sub_element)
            
                if found_elements:
                    retry_count += 1
                    print_status(f"Retry {retry_count}/{max_retries} - Countries to re-download: {', '.join(found_elements)}", "warning")
                    print_status(f"Problematic files: {', '.join(check_to_redownload)}", "warning")
                    download_part(list(found_elements), only_store_s, stores)
                else:
                    print_status("All files downloaded successfully! ✨", "success")
                    break
            
            if retry_count >= max_retries:
                print_status(f"Maximum retries ({max_retries}) reached. Some files may still have issues.", "warning")

            # File processing section with progress
            print(f"\n{Fore.MAGENTA}📊 PROCESSING DOWNLOADED FILES...{Style.RESET_ALL}")
            
            csv_files = [f for f in os.listdir(wp_working_output ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
            
            print_status(f"Found {len(csv_files)} files to process", "info")
            
            if csv_files:
                print_status("Combining files into dataset...", "processing")
                
                # Show progress while reading files
                file_progress = tqdm(csv_files, desc="📂 Reading files", colour='blue')
                
                if what_to_download == "losses":
                    dataframes = []
                    for f in file_progress:
                        file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                        df = pd.read_csv(os.path.join(wp_working_output, f), sep=',', skiprows=6, skipfooter=1, engine='python')
                        dataframes.append(df)
                    
                    ce_df = pd.concat(dataframes, ignore_index=True)
                    
                    # Save the file
                    output_file = wp_working_output / f"{what_to_download}_{saved_name}_raw_data.parquet"
                    ce_df.to_parquet(output_file, compression="gzip")
                    print_status(f"Saved as parquet: {output_file}", "success")

                elif what_to_download == "srd":
                    dataframes = []
                    for f in file_progress:
                        file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                        df = pd.read_csv(os.path.join(wp_working_output, f), sep=',')
                        dataframes.append(df)
                    
                    ce_df = pd.concat(dataframes, ignore_index=True)
                    
                elif not what_to_download in ("losses", "srd"):
                    dataframes = []
                    for f in file_progress:
                        file_progress.set_postfix({"Current": f[:20] + "..." if len(f) > 20 else f})
                        df = pd.read_csv(os.path.join(wp_working_output, f), sep=',')
                        dataframes.append(df)
                    
                    ce_df = pd.concat(dataframes, ignore_index=True)
                
                file_progress.close()
                
                print_status(f"Final dataset shape: {ce_df.shape[0]:,} rows × {ce_df.shape[1]} columns", "success")
                
                
                
            # saving the file
            ce_df.to_parquet(wp_working_output / f"{what_to_download}_{saved_name}", compression="gzip")
                
            # removing the zip files
            files = [f for f in os.listdir(
                wp_working_output 
                ) if f.endswith('.zip') and f.__contains__(f"{what_to_download}")]
            
            for c in files:
                os.remove(
                    os.path.join(
                        wp_working_output / f"{c}"
                    )
                )
            
            
            # Final summary
            total_time = time.time() - start_time
            print(f"""
                        {Fore.GREEN}╔══════════════════════════════════════════════════════════════╗
                        ║                        🎉 DOWNLOAD COMPLETE! 🎉               ║
                        ║                                                              ║
                        ║  Total Time: {total_time/60:.1f} minutes                                ║
                        ║  Files Processed: {len(csv_files) if csv_files else 0}                                      ║
                        ║  Countries: {', '.join(countries)}                                  ║
                        ║  Mode: {'Store-specific' if only_store_s else 'All groups'}                                    ║
                        ╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
                        """)        

            return ce_df
        
        sql = ssh_table_create("srd", None, None, None, None, place_to_save, saved_filename)        
        
        sql= 0
        if sql ==0:
                            
            srdTable_CE = ssh_downloader("srd", place_to_save, saved_filename, stores)

        

        srdTable_CE = srdTable_CE.fillna(0)
        
        

    SRD_tables = SRD_db()
    
    # SRD_tables = gsd.SRD_table_added_hierarchy(SRD_tables)
    
    return SRD_tables, folder_name


SRD_tables, folder_name = JDA_SRD_CE_report()

# SRD_tables = rmf.optimize_objects(rmf.optimize_types(SRD_tables))
# SRD_tables = SRD_tables[SRD_tables.Store_Format != 0]



# SRD_tables.to_csv(
#     directory / srd_tables_places / f"{folder_name}/CE_JDA_SRD.csv.gzip", compression="gzip", index=False
# )

opsdev, foil = gsd.SRD_to_opsdev(pd.read_parquet(directory / srd_tables_places / f"{folder_name}/srd_{saved_filename}"), directory, excel_inputs_f )



opsdev = rmf.optimize_objects(rmf.optimize_types(opsdev))




opsdev_old, foil_old = gsd.SRD_to_opsdev_old(pd.read_parquet(directory / srd_tables_places / f"{folder_name}/srd_{saved_filename}"), directory, excel_inputs_f )









products_for_product_names = opsdev[['country', 'tpnb']].drop_duplicates().groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()

# Print country and count of products with missing sold units
for key, value in products_for_product_names.items():
    print(key, len([item for item in value if item]))


product_name = pd.DataFrame()
with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    # Use the same variable name here as defined above
    for k, v in products_for_product_names.items():
        
        s = list()
        
        for x in v:
            s.append(str(x))
        
        tpnbs = ",".join(s)
        
        sql = f"""                          
            SELECT
                mstr.cntr_code AS country,
                mstr.slad_tpnb as tpnb,
                mstr.slad_long_des as product_name

            FROM DM.dim_artgld_details mstr 

            WHERE 
                mstr.slad_tpnb in ({tpnbs})
                AND     
                mstr.cntr_code = '{k}' 

            GROUP BY mstr.cntr_code,  mstr.slad_tpnb, mstr.slad_long_des
        """
        
        # Read SQL results and concatenate to the final dataframe
        product_name = pd.concat([product_name, pd.read_sql(sql, conn)])



product_name["tpnb"] = product_name["tpnb"].astype("int")

opsdev = opsdev.merge(product_name, on=["country", "tpnb"], how = "left") 
opsdev_old = opsdev_old.merge(product_name, on=["country", "tpnb"], how = "left")   

opsdev.to_parquet(
    directory / srd_tables_places / f"{folder_name}/CE_JDA_SRD_for_model_%_way", compression="gzip"
)

foil.to_parquet(
    directory / srd_tables_places / f"{folder_name}/foil", compression="gzip"
)


opsdev_old.to_parquet(
    directory / srd_tables_places / f"{folder_name}/CE_JDA_SRD_for_model", compression="gzip"
)

    
 

# opsdev_old = r"c:\Users\<USER>\OneDrive - Tesco\#MODELS\#REPLENISHMENT\ReplModel_2025\inputs\JDA_SRD_Tables\03-01-2025\CE_JDA_SRD_for_model"


# opsdev_old = pd.read_parquet(opsdev_old)

# df_for_charts = gsd.tpnb_charts_data(opsdev_old, period, act_dataset, directory, excel_inputs_f,  category_reset_df, year)

# df_for_charts.to_parquet(
#     directory / srd_tables_places / f"{folder_name}/df_for_charts"
# )

# =============================================================================
# if you need to make a comparison to 'AS IS' model
# =============================================================================
# opsdev.columns = (opsdev.iloc[:, :4].columns.tolist()
#                   + opsdev.iloc[:, 4:10].add_suffix("_new").columns.tolist()
#                   + opsdev.iloc[:,10:].columns.tolist())

# opsdev.drop("product_name", axis=1, inplace=True)


# modelDataSet_as_is_ = modelDataSet_as_is.merge(opsdev[opsdev.iloc[:,:9].columns.tolist()], on=['country',
#                                                                                               'store', 
#                                                                                               'tpnb'], how='left')

# for x, y in zip(["srp_new", "nsrp_new", "mu_new", "full_pallet_new", "split_pallet_new", "icream_nsrp_new"], ['srp', 'nsrp', 'mu', 'full_pallet', 'split_pallet', "icream_nsrp"]):
#     cond = [modelDataSet_as_is_[x].notnull()]
#     result = [modelDataSet_as_is_[x]]
#     modelDataSet_as_is_[y] = np.select(cond, result, modelDataSet_as_is_[y])
#     modelDataSet_as_is_.drop(x, axis=1, inplace=True)
    
# rmf.optimize_objects(rmf.optimize_types(modelDataSet_as_is_)).to_parquet(directory / srd_tables_places / f"{folder_name}/as_is_modelDataSet_updated_{folder_name[:5]}", compression="gzip"
# )






df_old = pd.read_parquet(old_f)

df_new = opsdev_old.copy()

column_needed = ["country", "tpnb","product_name", "division", "DIV_ID", "department", "DEP_ID", "section", "SEC_ID", "group","GRP_ID","subgroup", "SGR_ID","pmg","dep","supplier_name","supplier_id","supplier_code","own_brand"]
column_needed_later = ["division", "DIV_ID", "department", "DEP_ID", "section", "SEC_ID", "group","GRP_ID","subgroup", "SGR_ID","pmg","dep","supplier_name","supplier_id","supplier_code","own_brand"]


df_new = df_new.groupby(["country", "tpnb"], as_index=False, observed=True)[["srp","nsrp","mu","full_pallet","split_pallet"]].sum()

df_merged = df_new.merge(df_old[column_needed].drop_duplicates(), on=['country', 'tpnb'], how='left')


   
missing_df = df_merged[df_merged.pmg.isnull()]





products = missing_df[['country', 'tpnb']].drop_duplicates().groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()

for key, value in products.items():
    #print value
    print(key, len([item for item in value if item]))

pmg_tpn = pd.DataFrame()

with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:


        

        for k, v in products.items():
                            
            s = list()
            
            for x in v:
                        
                s.append(str(x))
                tpnbs = ",".join(s)
            
            sql = """ SELECT 
                            tpns.cntr_code AS country,
                            cast(tpns.slad_tpnb AS INT) AS tpnb,
                            tpns.dmat_div_des_en AS division,
                            cast(tpns.dmat_div_code as INT) as DIV_ID,
                            tpns.dmat_dep_des_en AS department,
                            cast(tpns.dmat_dep_code as INT) as DEP_ID,
                            tpns.dmat_sec_des_en AS section,
                            cast(tpns.dmat_sec_code as INT) as SEC_ID,
                            tpns.dmat_grp_des_en AS group,
                            cast(tpns.dmat_grp_code as INT) as GRP_ID,
                            tpns.dmat_sgr_des_en AS subgroup,
                            cast(tpns.dmat_sgr_code as INT) as SGR_ID,
                            supl.dmsup_long_des as supplier_name,
                            supl.dmsup_id as supplier_id,
                            supl.dmsup_code as supplier_code,
                            tpns.own_brand as own_brand,


                            hier.pmg as pmg
                    FROM
                            DM.dim_artgld_details tpns
                            
                    LEFT JOIN tesco_analysts.hierarchy_spm hier
                    ON tpns.dmat_div_code = LPAD(hier.div_code,4,"0")
                    AND tpns.dmat_dep_code = LPAD(hier.dep_code,4,"0")
                    AND tpns.dmat_sec_code = LPAD(hier.sec_code,4,"0")
                    AND tpns.dmat_grp_code = LPAD(hier.grp_code,4,"0")
                    AND tpns.dmat_sgr_code = LPAD(hier.sgr_code,4,"0")
                    LEFT JOIN
                        dw.dim_suppliers supl
                    ON
                        supl.dmsup_cntr_id = tpns.cntr_id 
                    AND
                        supl.dmsup_id = tpns.slad_dmsup_id
                    
                    WHERE 
                            slad_tpnb in ({tpnbs}) 
                    AND     
                            cntr_code = '{k}' 
                    AND     
                            dmat_sgr_des_en <> "Do not use"
                    GROUP BY 
                            cntr_code,
                            slad_tpnb,
                            dmat_div_des_en,
                            dmat_div_code,
                            dmat_dep_des_en,
                            dmat_dep_code,
                            dmat_sec_des_en,
                            dmat_sec_code,
                            dmat_grp_des_en,
                            dmat_grp_code,
                            dmat_sgr_des_en,
                            dmat_sgr_code,
                            supl.dmsup_long_des,
                            supl.dmsup_id,
                            supl.dmsup_code,
                            hier.pmg,
                            tpns.own_brand
                            
                            """.format(
                tpnbs=tpnbs, k=k
            )

            pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])
            
            
            
notna_df = df_merged[df_merged.pmg.notnull()]

missing_df.drop(column_needed_later, axis=1, inplace=True)
                    
missing_df = missing_df.merge(pmg_tpn, on=["country","tpnb"], how="left")

df = pd.concat([notna_df, missing_df])





def fill_missing_pmg(df, hierarchical=True):
    """
    Fill missing PMG values based on DIV_ID, DEP_ID, SEC_ID, and GRP_ID combinations
    with an option for hierarchical fallback.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The dataframe containing the columns DIV_ID, DEP_ID, SEC_ID, GRP_ID, and pmg
    hierarchical : bool, default=True
        If True, uses a hierarchical approach to fill missing values by progressively
        using fewer ID fields when exact matches aren't found
        
    Returns:
    --------
    pandas.DataFrame
        DataFrame with filled pmg values
    """
    df_result = df.copy()
    
    # Step 1: Try with all 4 keys (DIV_ID + DEP_ID + SEC_ID + GRP_ID)
    df_result['composite_key'] = df_result['DIV_ID'].astype(str) + '_' + \
                                df_result['DEP_ID'].astype(str) + '_' + \
                                df_result['SEC_ID'].astype(str) + '_' + \
                                df_result['GRP_ID'].astype(str)
    
    # Create mapping dictionaries for each level
    valid_pmg_data = df_result[df_result['pmg'].notna()]
    
    # First level mapping (all 4 fields)
    level1_mapping = {}
    for composite_key, group_df in valid_pmg_data.groupby('composite_key'):
        pmg_values = group_df['pmg'].value_counts()
        if not pmg_values.empty:
            level1_mapping[composite_key] = pmg_values.index[0]
    
    # Apply first level mapping
    initial_missing = df_result['pmg'].isna().sum()
    missing_pmg_mask = df_result['pmg'].isna()
    missing_keys = df_result.loc[missing_pmg_mask, 'composite_key']
    for key in missing_keys.unique():
        if key in level1_mapping:
            df_result.loc[(missing_pmg_mask) & (df_result['composite_key'] == key), 'pmg'] = level1_mapping[key]
    
    # Remove the temporary composite key
    df_result.drop('composite_key', axis=1, inplace=True)
    
    # If hierarchical=True, try fallback levels
    first_level_filled = initial_missing - df_result['pmg'].isna().sum()
    still_missing = df_result['pmg'].isna().sum()
    
    fill_stats = {
        'level1': first_level_filled
    }
    
    if hierarchical and still_missing > 0:
        # Try progressively with fewer keys
        
        # Level 2: DIV_ID + DEP_ID + SEC_ID
        df_result['l2_key'] = df_result['DIV_ID'].astype(str) + '_' + \
                             df_result['DEP_ID'].astype(str) + '_' + \
                             df_result['SEC_ID'].astype(str)
        
        l2_mapping = {}
        valid_data = df_result[df_result['pmg'].notna()]
        for key, group_df in valid_data.groupby('l2_key'):
            pmg_values = group_df['pmg'].value_counts()
            if not pmg_values.empty:
                l2_mapping[key] = pmg_values.index[0]
        
        # Apply level 2 mapping
        before_l2 = df_result['pmg'].isna().sum()
        missing_mask = df_result['pmg'].isna()
        for key in df_result.loc[missing_mask, 'l2_key'].unique():
            if key in l2_mapping:
                df_result.loc[missing_mask & (df_result['l2_key'] == key), 'pmg'] = l2_mapping[key]
        
        fill_stats['level2'] = before_l2 - df_result['pmg'].isna().sum()
        df_result.drop('l2_key', axis=1, inplace=True)
        
        # Level 3: DIV_ID + DEP_ID
        if df_result['pmg'].isna().sum() > 0:
            df_result['l3_key'] = df_result['DIV_ID'].astype(str) + '_' + \
                                 df_result['DEP_ID'].astype(str)
            
            l3_mapping = {}
            valid_data = df_result[df_result['pmg'].notna()]
            for key, group_df in valid_data.groupby('l3_key'):
                pmg_values = group_df['pmg'].value_counts()
                if not pmg_values.empty:
                    l3_mapping[key] = pmg_values.index[0]
            
            # Apply level 3 mapping
            before_l3 = df_result['pmg'].isna().sum()
            missing_mask = df_result['pmg'].isna()
            for key in df_result.loc[missing_mask, 'l3_key'].unique():
                if key in l3_mapping:
                    df_result.loc[missing_mask & (df_result['l3_key'] == key), 'pmg'] = l3_mapping[key]
                    
            fill_stats['level3'] = before_l3 - df_result['pmg'].isna().sum()
            df_result.drop('l3_key', axis=1, inplace=True)
            
        # Level 4: DEP_ID + SEC_ID (sometimes product categories are better predictors)
        if df_result['pmg'].isna().sum() > 0:
            df_result['l4_key'] = df_result['DEP_ID'].astype(str) + '_' + \
                                 df_result['SEC_ID'].astype(str)
            
            l4_mapping = {}
            valid_data = df_result[df_result['pmg'].notna()]
            for key, group_df in valid_data.groupby('l4_key'):
                pmg_values = group_df['pmg'].value_counts()
                if not pmg_values.empty:
                    l4_mapping[key] = pmg_values.index[0]
            
            # Apply level 4 mapping
            before_l4 = df_result['pmg'].isna().sum()
            missing_mask = df_result['pmg'].isna()
            for key in df_result.loc[missing_mask, 'l4_key'].unique():
                if key in l4_mapping:
                    df_result.loc[missing_mask & (df_result['l4_key'] == key), 'pmg'] = l4_mapping[key]
                    
            fill_stats['level4'] = before_l4 - df_result['pmg'].isna().sum()
            df_result.drop('l4_key', axis=1, inplace=True)
            
        # Level 5: subgroup mapping (as a last resort)  
        if df_result['pmg'].isna().sum() > 0 and 'subgroup' in df_result.columns:
            before_l5 = df_result['pmg'].isna().sum()
            
            # Get mapping from subgroup to pmg
            subgroup_mapping = {}
            valid_data = df_result[df_result['pmg'].notna()]
            for subgroup, group_df in valid_data.groupby('subgroup'):
                pmg_values = group_df['pmg'].value_counts()
                if not pmg_values.empty:
                    subgroup_mapping[subgroup] = pmg_values.index[0]
            
            # Apply subgroup mapping
            missing_mask = df_result['pmg'].isna()
            for subgroup in df_result.loc[missing_mask, 'subgroup'].unique():
                if subgroup in subgroup_mapping:
                    df_result.loc[missing_mask & (df_result['subgroup'] == subgroup), 'pmg'] = subgroup_mapping[subgroup]
                    
            fill_stats['level5'] = before_l5 - df_result['pmg'].isna().sum()
    
    # Report fill statistics
    total_missing = initial_missing
    still_missing = df_result['pmg'].isna().sum()
    total_filled = total_missing - still_missing
    
    print(f"PMG Fill Statistics:")
    print(f"Total missing values: {total_missing}")
    print(f"Values filled: {total_filled}")
    if hierarchical:
        for level, count in fill_stats.items():
            print(f"  - {level}: {count} values filled")
    print(f"Values still missing: {still_missing}")
    print(f"Overall fill rate: {total_filled/total_missing*100:.2f}% (if total_missing > 0)")
    
    return df_result





df_country_level = fill_missing_pmg(df, hierarchical=True)
df_filled = df_country_level[df_country_level.pmg.notna()]
df_country_level["dep"] = df_country_level["pmg"].str[:3]
df_country_level["period"] = period


# volume_from_act_dataset = pd.read_parquet(act_dataset).groupby(["store", "tpnb"], as_index=False, observed=True)["sold_units"].sum()


# df_filled = opsdev.merge(volume_from_act_dataset, on=["store", "tpnb"], how="left")

# no_sold_unit = df_filled[df_filled["sold_units"].isna()]

products_for_sold_units = opsdev[['country', 'tpnb']].drop_duplicates().groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()

# Print country and count of products with missing sold units
for key, value in products_for_sold_units.items():
    print(key, len([item for item in value if item]))

pmg_tpn = pd.DataFrame()
with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    # Use the same variable name here as defined above
    for k, v in products_for_sold_units.items():
        
        s = list()
        
        for x in v:
            s.append(str(x))
        
        tpnbs = ",".join(s)
        
        sql = f"""                          
            SELECT
                mstr.cntr_code AS country,
                CAST(stores.dmst_store_code AS INT) AS store,  

                mstr.slad_tpnb as tpnb,
                
                SUM(sunit.slsms_unit)/ {nr_weeks} AS sold_units
            FROM dw.sl_sms sunit 
            JOIN DM.dim_artgld_details mstr ON mstr.slad_dmat_id = sunit.slsms_dmat_id AND mstr.cntr_id = sunit.slsms_cntr_id
            LEFT JOIN dm.dim_time_d cal ON cal.dmtm_d_code = sunit.part_col
            JOIN dm.dim_stores stores ON stores.cntr_id = sunit.slsms_cntr_id AND stores.dmst_store_id = sunit.slsms_dmst_id

            WHERE 
                mstr.slad_tpnb in ({tpnbs})
                AND     
                mstr.cntr_code = '{k}' 
                AND
                cal.dmtm_fw_code BETWEEN {start_date} AND {end_date}
                AND
                sunit.slsms_unit > 0
            GROUP BY mstr.cntr_code, stores.dmst_store_code, mstr.slad_tpnb
        """
        
        # Read SQL results and concatenate to the final dataframe
        pmg_tpn = pd.concat([pmg_tpn, pd.read_sql(sql, conn)])





# no_sold_unit.drop("sold_units", axis=1, inplace=True)
pmg_tpn["tpnb"] = pmg_tpn["tpnb"].astype("int")
opsdev_with_sales = opsdev.merge(pmg_tpn[["store", "tpnb", "sold_units"]].drop_duplicates(), on=["store", "tpnb"], how="left")
opsdev_with_sales.loc[opsdev_with_sales.sold_units.isna(), "sold_units"] = 0


# df_filled = pd.concat([df_filled[df_filled["sold_units"].notna()], no_sold_unit])

# Reset the index
# df_filled = df_filled.reset_index(drop=True)

# Then try your operations again
opsdev_with_sales.loc[opsdev_with_sales.icream_nsrp > 0, "nsrp"] = opsdev_with_sales.icream_nsrp + opsdev_with_sales.nsrp
opsdev_with_sales.loc[opsdev_with_sales.icream_nsrp > 0, "icream_nsrp"] = 0




repl_types = ["srp", "nsrp", "full_pallet", "mu", "split_pallet"]


for r in repl_types:
    opsdev_with_sales[r] = np.where(opsdev_with_sales[r] !=0, opsdev_with_sales.sold_units * opsdev_with_sales[r], 0)
    
    
opsdev_with_sales = opsdev_with_sales.groupby(["country", "tpnb"], as_index=False, observed=True)[repl_types].sum()

# Create a dictionary mapping current column names to new names with "_sales" suffix
new_names = {col: col + "_sales" for col in repl_types}

# Rename the columns in df_filled
opsdev_with_sales = opsdev_with_sales.rename(columns=new_names)

final_df = df_country_level.merge(opsdev_with_sales, on=["country", "tpnb"], how="left")


products_for_product_names = final_df[['country', 'tpnb']].drop_duplicates().groupby(["country"], observed=True)['tpnb'].apply(lambda s: s.tolist()).to_dict()

# Print country and count of products with missing sold units
for key, value in products_for_product_names.items():
    print(key, len([item for item in value if item]))


product_name = pd.DataFrame()
with pyodbc.connect(
    "DSN=UKHadoop_Resource_SCH", autocommit=True, Trusted_Connection="yes"
) as conn:
    
    # Use the same variable name here as defined above
    for k, v in products_for_product_names.items():
        
        s = list()
        
        for x in v:
            s.append(str(x))
        
        tpnbs = ",".join(s)
        
        sql = f"""                          
            SELECT
                mstr.cntr_code AS country,
                mstr.slad_tpnb as tpnb,
                mstr.slad_long_des as product_name

            FROM DM.dim_artgld_details mstr 

            WHERE 
                mstr.slad_tpnb in ({tpnbs})
                AND     
                mstr.cntr_code = '{k}' 

            GROUP BY mstr.cntr_code,  mstr.slad_tpnb, mstr.slad_long_des
        """
        
        # Read SQL results and concatenate to the final dataframe
        product_name = pd.concat([product_name, pd.read_sql(sql, conn)])



product_name["tpnb"] = product_name["tpnb"].astype("int")

final_df.drop("product_name", axis=1, inplace=True)

final_df = final_df.merge(product_name, on=["country", "tpnb"], how = "left") 



final_df.to_parquet( directory / srd_tables_places /f"{folder_name}/CE_JDA_SRD_for_streamlit_{period}", compression="gzip")














def find_missing_products(df_p11, df_p12):
    """
    Find products that are in period 11 but not in period 12, and vice versa.
    
    Parameters:
    df_p11 (pandas.DataFrame): DataFrame for period 11
    df_p12 (pandas.DataFrame): DataFrame for period 12
    
    Returns:
    tuple: (in_p11_not_in_p12, in_p12_not_in_p11)
    """
    # Create composite keys for comparison (country + tpnb)
    df_p11['composite_key'] = df_p11['country'] + '_' + df_p11['tpnb'].astype(str)
    df_p12['composite_key'] = df_p12['country'] + '_' + df_p12['tpnb'].astype(str)
    
    # Get sets of keys for each period
    p11_keys = set(df_p11['composite_key'])
    p12_keys = set(df_p12['composite_key'])
    
    # Find keys in p11 but not in p12
    keys_in_p11_not_in_p12 = p11_keys - p12_keys
    
    # Find keys in p12 but not in p11
    keys_in_p12_not_in_p11 = p12_keys - p11_keys
    
    # Filter the original dataframes
    in_p11_not_in_p12 = df_p11[df_p11['composite_key'].isin(keys_in_p11_not_in_p12)]
    in_p12_not_in_p11 = df_p12[df_p12['composite_key'].isin(keys_in_p12_not_in_p11)]
    
    # Drop the temporary composite key
    if not in_p11_not_in_p12.empty:
        in_p11_not_in_p12 = in_p11_not_in_p12.drop('composite_key', axis=1)
    if not in_p12_not_in_p11.empty:
        in_p12_not_in_p11 = in_p12_not_in_p11.drop('composite_key', axis=1)
    
    return in_p11_not_in_p12, in_p12_not_in_p11




# Example usage:
# delisted_products, new_products = find_missing_products(a, b)




