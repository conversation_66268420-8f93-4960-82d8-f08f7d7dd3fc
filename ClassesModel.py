import pandas as pd

# =============================================================================
# True False values for Replenishment Model
# =============================================================================
class ModelTrueFalse:
    def __init__(
        self,
        DATASET_TPN_FUNC: bool,
        DATASET_TPN_SAVE: bool,
        only_tpn: bool,
        tpnb_store: bool,
        tpnb_country: bool,
        OPB_DIV_SAVE: bool,
        calc_sheet_insight: bool,
        INSIGHT_DIFF: bool,
        INSIGHT_BUSINESS: bool,
        broken_case: bool,
        Run_Dataset_only: bool,
        Run_CE_Charts: bool,
        Cost_Base: bool,
        news_HU: bool,
        shelfService_gm: bool,
        cases_to_replenish_only: bool,
        OPB: bool
    ):

        # Model
        self.Run_Dataset_only = Run_Dataset_only
        self.DATASET_TPN_FUNC = DATASET_TPN_FUNC
        self.DATASET_TPN_SAVE = DATASET_TPN_SAVE

        self.broken_case = broken_case

        # TPNB
        self.only_tpn = only_tpn
        self.tpnb_store = tpnb_store
        self.tpnb_country = tpnb_country

        # Save
        self.OPB_DEP_SAVE = OPB
        self.OPB_DIV_SAVE = OPB_DIV_SAVE
        self.INSIGHT_SAVE = OPB
        self.calc_sheet_insight = calc_sheet_insight
        self.INSIGHT_DIFF = INSIGHT_DIFF
        self.OPB_DEP_DIFF_SAVE = OPB
        self.INSIGHT_BUSINESS = INSIGHT_BUSINESS
        self.Run_CE_Charts = Run_CE_Charts
        self.Cost_Base = Cost_Base
        self.news_HU = news_HU
        self.shelfService_gm = shelfService_gm
        self.cases_to_replenish_only = cases_to_replenish_only
        


# =============================================================================
# paths of Data for Replenishment Model
# =============================================================================
class Data_Paths:
    def __init__(
        self,
        directory,
        repl_dataset_f: str,
        items_sold_f: str,
        items_sold_dotcom: str,
        stock_f: str,
        cases_f: str,
        ops_dev_f: str,
        box_op_type_f: str,
        excel_inputs_f: str,
        pallet_capacity_f: str,
        losses_f: str,
        most_f: str,
        act_model_outputs: str,
        act_model_insights: str,
        selected_tpn: str,
        stores: list,
        wh_prof_drivers: str,
        wh_most: str,
        wh_cases_pmg: str,
        wh_pattisserie: str,
        wh_deliveries: str,
        broken_cases_f: str,
        act_model_cost_base: str,
        single_pick_f: str,
        foil_f: str,
        shelfCapacity: str,
        presort_pal_del: str,
        night_truck_helper: str
    ):

        self.directory = directory
        self.repl_dataset_f = repl_dataset_f
        self.items_sold_f = items_sold_f
        self.items_sold_dotcom = items_sold_dotcom
        self.stock_f = stock_f
        self.cases_f = cases_f
        self.ops_dev_f = ops_dev_f
        self.box_op_type_f = box_op_type_f

        self.excel_inputs_f = excel_inputs_f
        self.pallet_capacity_f = pallet_capacity_f
        self.losses_f = losses_f
        self.most_f = most_f

        self.act_model_outputs = act_model_outputs
        self.act_model_insights = act_model_insights

        self.selected_tpn = selected_tpn

        self.stores = stores

        self.wh_prof_drivers = wh_prof_drivers
        self.wh_most = wh_most
        self.wh_cases_pmg = wh_cases_pmg
        self.wh_pattisserie = wh_pattisserie
        self.wh_deliveries = wh_deliveries

        self.broken_cases_f = broken_cases_f
        self.act_model_cost_base = act_model_cost_base
        
        self.single_pick_f = single_pick_f
        self.foil_f = foil_f
        self.shelfCapacity = shelfCapacity
        self.presort_pal_del = presort_pal_del
        self.night_truck_helper = night_truck_helper
        
# =============================================================================
#  True False values for fetching volumes, data etc. from systems       
# =============================================================================
class SQL_Part:
    def __init__(
        self,
        start_date: str,
        end_date: str,
        nr_weeks: int,
        pmg_list: str,
        country: str,
        item_sold_sql: bool,
        stock_sql: bool,
        cases_sql: bool,
        opsdev_sql: bool,
        planogram_sql: bool,
        saved_filename: str,
        date_plan: int,
        place_to_save,
        pallet_capacity: bool,
        losses_RTC_FoodBank_sql: bool,
        WGLL_gapScan: bool,
        WH_deliveries: bool,
        stores: int,
        Rc1_products: bool,
        WH_cases_PMG_level: bool,
        ob_packaging: bool,
        download_create_dataset: bool,
        modul_nr: bool,
        download_create_dataset_combined: bool
    ):

        self.start_date = start_date
        self.end_date = end_date
        self.nr_weeks = nr_weeks
        self.pmg_list = pmg_list
        self.country = country
        self.item_sold_sql = item_sold_sql
        self.stock_sql = stock_sql
        self.cases_sql = cases_sql
        self.opsdev_sql = opsdev_sql
        self.planogram_sql = planogram_sql
        self.saved_filename = saved_filename
        self.date_plan = date_plan
        self.place_to_save = place_to_save
        self.pallet_capacity = pallet_capacity
        self.losses_RTC_FoodBank_sql = losses_RTC_FoodBank_sql
        self.WGLL_gapScan = WGLL_gapScan
        self.WH_deliveries = WH_deliveries
        self.stores = stores
        self.Rc1_products = Rc1_products
        self.WH_cases_PMG_level = WH_cases_PMG_level
        self.ob_packaging = ob_packaging
        self.download_create_dataset = download_create_dataset
        self.modul_nr = modul_nr
        self.download_create_dataset_combined = download_create_dataset_combined




# =============================================================================
# Summary (displays summary info on the console) for Replenishment Model
# =============================================================================
class ModelSummary:
    def __init__(
        self,
        startCode: int,
        endCode: int,
        hrs_comparison: pd.DataFrame,
        insight_diff_act_groups: pd.DataFrame,
        repl_wh_hours: pd.DataFrame,
    ):

        self.minCode = ((endCode - startCode) / 60) // 1
        self.secCode = round((((endCode - startCode) / 60) - self.minCode) * 60)

        self.hrs_comparison = hrs_comparison
        self.insight_diff_act_groups = insight_diff_act_groups

        self.op_deptotal = hrs_comparison["diff_hours"].sum()
        self.insight_diff_total = insight_diff_act_groups["DIFF_IN_HOURS"].sum()

        self.repl_wh_hours = repl_wh_hours

        self.op_deptotal_gbp = hrs_comparison["diff_in_GBP"].sum()
        
# =============================================================================
# What if calculations for Merchinding styles changing
# =============================================================================
class What_Ifs_Inputs:
    def __init__(
        self,
        sheet_name: str,
        repl_type: str,
        TPN_Cost_Base: bool,
        opening_type: bool,
        volume_modifier: bool,
        case_cap_modifier: bool,
        shelf_capacity_modifier: bool,
        chunk_size: int,
        only_WH: bool
    ):

        self.sheet_name = sheet_name
        self.repl_type = repl_type
        self.TPN_Cost_Base = TPN_Cost_Base
        self.opening_type = opening_type
        self.volume_modifier = volume_modifier
        self.case_cap_modifier = case_cap_modifier
        self.shelf_capacity_modifier = shelf_capacity_modifier
        self.chunk_size = chunk_size
        self.only_WH = only_WH