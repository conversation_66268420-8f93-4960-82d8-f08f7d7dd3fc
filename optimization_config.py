"""
Configuration file for Replenishment Model optimizations.

This file allows easy switching between different implementations and 
performance tuning options.
"""

import os
from pathlib import Path

class OptimizationConfig:
    """Configuration class for optimization settings"""
    
    def __init__(self):
        # Main optimization switches
        self.USE_OPTIMIZED_DRIVERS = self._get_bool_env('USE_OPTIMIZED_DRIVERS', True)
        self.USE_POLARS_BACKEND = self._get_bool_env('USE_POLARS_BACKEND', True)
        self.ENABLE_PERFORMANCE_LOGGING = self._get_bool_env('ENABLE_PERFORMANCE_LOGGING', True)
        
        # Memory optimization settings
        self.OPTIMIZE_MEMORY_USAGE = self._get_bool_env('OPTIMIZE_MEMORY_USAGE', True)
        self.USE_CATEGORICAL_DTYPES = self._get_bool_env('USE_CATEGORICAL_DTYPES', True)
        self.ENABLE_LAZY_EVALUATION = self._get_bool_env('ENABLE_LAZY_EVALUATION', True)
        
        # Performance tuning
        self.BATCH_SIZE = self._get_int_env('BATCH_SIZE', 100)  # For fallback batching if needed
        self.MAX_MEMORY_GB = self._get_float_env('MAX_MEMORY_GB', 8.0)  # Memory limit
        self.PARALLEL_PROCESSING = self._get_bool_env('PARALLEL_PROCESSING', False)  # Future enhancement
        
        # Debugging and testing
        self.DEBUG_MODE = self._get_bool_env('DEBUG_MODE', False)
        self.PROFILE_PERFORMANCE = self._get_bool_env('PROFILE_PERFORMANCE', False)
        self.VALIDATE_RESULTS = self._get_bool_env('VALIDATE_RESULTS', False)  # Compare outputs
        
        # Fallback behavior
        self.FALLBACK_ON_ERROR = self._get_bool_env('FALLBACK_ON_ERROR', True)
        self.FALLBACK_TIMEOUT_MINUTES = self._get_float_env('FALLBACK_TIMEOUT_MINUTES', 30.0)
    
    def _get_bool_env(self, key, default):
        """Get boolean environment variable"""
        value = os.getenv(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    def _get_int_env(self, key, default):
        """Get integer environment variable"""
        try:
            return int(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    def _get_float_env(self, key, default):
        """Get float environment variable"""
        try:
            return float(os.getenv(key, str(default)))
        except ValueError:
            return default
    
    def print_config(self):
        """Print current configuration"""
        print("🔧 Optimization Configuration:")
        print(f"   USE_OPTIMIZED_DRIVERS: {self.USE_OPTIMIZED_DRIVERS}")
        print(f"   USE_POLARS_BACKEND: {self.USE_POLARS_BACKEND}")
        print(f"   ENABLE_PERFORMANCE_LOGGING: {self.ENABLE_PERFORMANCE_LOGGING}")
        print(f"   OPTIMIZE_MEMORY_USAGE: {self.OPTIMIZE_MEMORY_USAGE}")
        print(f"   BATCH_SIZE: {self.BATCH_SIZE}")
        print(f"   FALLBACK_ON_ERROR: {self.FALLBACK_ON_ERROR}")
        if self.DEBUG_MODE:
            print(f"   🐛 DEBUG_MODE: {self.DEBUG_MODE}")

# Global configuration instance
config = OptimizationConfig()

# Performance monitoring utilities
class PerformanceMonitor:
    """Utility class for monitoring performance"""
    
    def __init__(self, enabled=True):
        self.enabled = enabled
        self.start_time = None
        self.checkpoints = []
    
    def start(self, description="Process"):
        """Start monitoring"""
        if self.enabled:
            import time
            self.start_time = time.time()
            self.checkpoints = []
            print(f"🚀 Starting: {description}")
    
    def checkpoint(self, description):
        """Add a checkpoint"""
        if self.enabled and self.start_time:
            import time
            elapsed = time.time() - self.start_time
            self.checkpoints.append((description, elapsed))
            print(f"⏱️  {description}: {elapsed:.2f}s")
    
    def finish(self, description="Process"):
        """Finish monitoring"""
        if self.enabled and self.start_time:
            import time
            total_time = time.time() - self.start_time
            print(f"✅ Completed: {description} in {total_time:.2f}s")
            return total_time
        return None

# Memory monitoring utilities
class MemoryMonitor:
    """Utility class for monitoring memory usage"""
    
    def __init__(self, enabled=True):
        self.enabled = enabled
        self.initial_memory = None
    
    def start(self):
        """Start memory monitoring"""
        if self.enabled:
            try:
                import psutil
                import os
                process = psutil.Process(os.getpid())
                self.initial_memory = process.memory_info().rss / 1024 / 1024  # MB
                print(f"💾 Initial memory: {self.initial_memory:.1f} MB")
            except ImportError:
                print("⚠️  psutil not available for memory monitoring")
                self.enabled = False
    
    def checkpoint(self, description):
        """Memory checkpoint"""
        if self.enabled and self.initial_memory:
            try:
                import psutil
                import os
                process = psutil.Process(os.getpid())
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                delta = current_memory - self.initial_memory
                print(f"💾 {description}: {current_memory:.1f} MB (+{delta:.1f} MB)")
                return current_memory
            except:
                return None
        return None

# Utility functions for optimization
def optimize_dataframe_memory(df, categorical_threshold=0.5):
    """Optimize DataFrame memory usage"""
    if not config.OPTIMIZE_MEMORY_USAGE:
        return df
    
    try:
        import pandas as pd
        
        # Convert object columns to categorical if they have low cardinality
        if config.USE_CATEGORICAL_DTYPES:
            for col in df.select_dtypes(include=['object']).columns:
                if df[col].nunique() / len(df) < categorical_threshold:
                    df[col] = df[col].astype('category')
        
        # Downcast numeric types
        for col in df.select_dtypes(include=['int64']).columns:
            df[col] = pd.to_numeric(df[col], downcast='integer')
        
        for col in df.select_dtypes(include=['float64']).columns:
            df[col] = pd.to_numeric(df[col], downcast='float')
        
        return df
    except Exception as e:
        print(f"⚠️  Memory optimization failed: {str(e)}")
        return df

def get_optimal_chunk_size(total_rows, available_memory_gb=None):
    """Calculate optimal chunk size based on available memory"""
    if available_memory_gb is None:
        available_memory_gb = config.MAX_MEMORY_GB
    
    # Rough estimate: 1GB can handle ~1M rows of typical data
    max_rows_per_gb = 1_000_000
    max_chunk_size = int(available_memory_gb * max_rows_per_gb * 0.8)  # 80% safety margin
    
    return min(total_rows, max_chunk_size, config.BATCH_SIZE * 1000)

# Export configuration and utilities
__all__ = [
    'config', 
    'OptimizationConfig', 
    'PerformanceMonitor', 
    'MemoryMonitor',
    'optimize_dataframe_memory',
    'get_optimal_chunk_size'
]

# Print configuration on import if debug mode is enabled
if config.DEBUG_MODE:
    config.print_config()
