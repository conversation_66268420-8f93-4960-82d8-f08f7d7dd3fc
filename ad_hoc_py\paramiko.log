DEB [20250714-13:16:48.768] thr=1   paramiko.transport: starting thread (client mode): 0xc7a9aff0
DEB [20250714-13:16:48.772] thr=1   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:16:48.929] thr=1   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:16:48.929] thr=1   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:16:48.953] thr=1   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:16:48.954] thr=1   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:16:48.955] thr=1   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:16:48.955] thr=1   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:16:48.956] thr=1   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:16:48.957] thr=1   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:16:48.957] thr=1   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:16:48.958] thr=1   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:16:48.959] thr=1   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:16:48.959] thr=1   paramiko.transport: client lang: <none>
DEB [20250714-13:16:48.960] thr=1   paramiko.transport: server lang: <none>
DEB [20250714-13:16:48.961] thr=1   paramiko.transport: kex follows: False
DEB [20250714-13:16:48.961] thr=1   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:16:48.962] thr=1   paramiko.transport: Strict kex mode: True
DEB [20250714-13:16:48.963] thr=1   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:16:48.964] thr=1   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:16:48.964] thr=1   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:16:48.965] thr=1   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:16:48.966] thr=1   paramiko.transport: Compression: none
DEB [20250714-13:16:48.967] thr=1   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:16:49.073] thr=1   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:16:49.074] thr=1   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:16:49.075] thr=1   paramiko.transport: Switch to new keys ...
DEB [20250714-13:16:49.077] thr=1   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:16:49.077] thr=2   paramiko.transport: Attempting password auth...
DEB [20250714-13:16:49.078] thr=1   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:16:49.215] thr=1   paramiko.transport: userauth is OK
INF [20250714-13:16:49.331] thr=1   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:16:49.333] thr=1   paramiko.transport: Auth banner: b'You are required to change your password immediately (password expired).\n'
INF [20250714-13:16:49.360] thr=1   paramiko.transport: Authentication (password) successful!
DEB [20250714-13:16:49.361] thr=2   paramiko.transport: [chan 0] Max packet in: 32768 bytes
DEB [20250714-13:16:49.389] thr=1   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:16:49.389] thr=1   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:16:49.459] thr=1   paramiko.transport: [chan 0] Max packet out: 32768 bytes
DEB [20250714-13:16:49.460] thr=1   paramiko.transport: Secsh channel 0 opened.
DEB [20250714-13:16:49.562] thr=1   paramiko.transport: [chan 0] Sesch channel 0 request ok
DEB [20250714-13:16:49.563] thr=1   paramiko.transport: [chan 0] EOF received (0)
DEB [20250714-13:16:49.565] thr=1   paramiko.transport: [chan 0] EOF sent (0)
DEB [20250714-13:18:31.649] thr=3   paramiko.transport: starting thread (client mode): 0xc7a57230
DEB [20250714-13:18:31.658] thr=3   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:18:31.778] thr=3   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:18:31.779] thr=3   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:18:31.817] thr=3   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:18:31.818] thr=3   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:18:31.819] thr=3   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:18:31.819] thr=3   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:18:31.820] thr=3   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:18:31.820] thr=3   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:18:31.822] thr=3   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:18:31.822] thr=3   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:18:31.823] thr=3   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:18:31.824] thr=3   paramiko.transport: client lang: <none>
DEB [20250714-13:18:31.825] thr=3   paramiko.transport: server lang: <none>
DEB [20250714-13:18:31.826] thr=3   paramiko.transport: kex follows: False
DEB [20250714-13:18:31.826] thr=3   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:18:31.827] thr=3   paramiko.transport: Strict kex mode: True
DEB [20250714-13:18:31.828] thr=3   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:18:31.828] thr=3   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:18:31.829] thr=3   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:18:31.829] thr=3   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:18:31.830] thr=3   paramiko.transport: Compression: none
DEB [20250714-13:18:31.831] thr=3   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:18:31.944] thr=3   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:18:31.944] thr=3   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:18:31.945] thr=3   paramiko.transport: Switch to new keys ...
DEB [20250714-13:18:31.946] thr=3   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:18:31.948] thr=2   paramiko.transport: Adding ssh-ed25519 host key for tpc84599bg1112.tgrc.tesco.org: b'6cfba7a142f6a8ca4586a24ffd280ce7'
DEB [20250714-13:18:31.948] thr=3   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:18:32.085] thr=3   paramiko.transport: userauth is OK
INF [20250714-13:18:32.219] thr=3   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:18:32.220] thr=3   paramiko.transport: Auth banner: b'You are required to change your password immediately (password expired).\n'
INF [20250714-13:18:32.248] thr=3   paramiko.transport: Authentication (password) successful!
DEB [20250714-13:18:32.249] thr=2   paramiko.transport: [chan 0] Max packet in: 32768 bytes
DEB [20250714-13:18:32.279] thr=3   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:18:32.280] thr=3   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:18:32.352] thr=3   paramiko.transport: [chan 0] Max packet out: 32768 bytes
DEB [20250714-13:18:32.354] thr=3   paramiko.transport: Secsh channel 0 opened.
DEB [20250714-13:18:32.466] thr=3   paramiko.transport: [chan 0] Sesch channel 0 request ok
DEB [20250714-13:18:32.470] thr=3   paramiko.transport: [chan 0] EOF received (0)
DEB [20250714-13:18:32.473] thr=2   paramiko.transport: [chan 0] EOF sent (0)
DEB [20250714-13:18:32.475] thr=2   paramiko.transport: Dropping user packet because connection is dead.
DEB [20250714-13:19:15.398] thr=4   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:19:15.404] thr=4   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:20:49.340] thr=5   paramiko.transport: starting thread (client mode): 0xc7f653a0
DEB [20250714-13:20:49.344] thr=5   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:20:49.481] thr=5   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:20:49.482] thr=5   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:20:49.510] thr=5   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:20:49.511] thr=5   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:20:49.512] thr=5   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:20:49.513] thr=5   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:20:49.514] thr=5   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:20:49.514] thr=5   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:20:49.516] thr=5   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:20:49.517] thr=5   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:20:49.517] thr=5   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:20:49.518] thr=5   paramiko.transport: client lang: <none>
DEB [20250714-13:20:49.519] thr=5   paramiko.transport: server lang: <none>
DEB [20250714-13:20:49.519] thr=5   paramiko.transport: kex follows: False
DEB [20250714-13:20:49.520] thr=5   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:20:49.521] thr=5   paramiko.transport: Strict kex mode: True
DEB [20250714-13:20:49.521] thr=5   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:20:49.522] thr=5   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:20:49.522] thr=5   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:20:49.523] thr=5   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:20:49.524] thr=5   paramiko.transport: Compression: none
DEB [20250714-13:20:49.525] thr=5   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:20:49.624] thr=5   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:20:49.626] thr=5   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:20:49.627] thr=5   paramiko.transport: Switch to new keys ...
DEB [20250714-13:20:49.627] thr=5   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:20:49.628] thr=2   paramiko.transport: Adding ssh-ed25519 host key for tpc84599bg1112.tgrc.tesco.org: b'6cfba7a142f6a8ca4586a24ffd280ce7'
DEB [20250714-13:20:49.629] thr=5   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:20:49.763] thr=5   paramiko.transport: userauth is OK
INF [20250714-13:20:53.604] thr=5   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:20:53.607] thr=5   paramiko.transport: Authentication (password) failed.
DEB [20250714-13:20:53.611] thr=5   paramiko.transport: EOF in transport thread
DEB [20250714-13:21:11.101] thr=6   paramiko.transport: starting thread (client mode): 0xcae78d70
DEB [20250714-13:21:11.106] thr=6   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:21:11.239] thr=6   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:21:11.240] thr=6   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:21:11.270] thr=6   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:21:11.270] thr=6   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:21:11.271] thr=6   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:21:11.272] thr=6   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:21:11.272] thr=6   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:21:11.273] thr=6   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:21:11.274] thr=6   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:21:11.274] thr=6   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:21:11.276] thr=6   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:21:11.276] thr=6   paramiko.transport: client lang: <none>
DEB [20250714-13:21:11.276] thr=6   paramiko.transport: server lang: <none>
DEB [20250714-13:21:11.277] thr=6   paramiko.transport: kex follows: False
DEB [20250714-13:21:11.278] thr=6   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:21:11.279] thr=6   paramiko.transport: Strict kex mode: True
DEB [20250714-13:21:11.279] thr=6   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:21:11.280] thr=6   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:21:11.281] thr=6   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:21:11.282] thr=6   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:21:11.282] thr=6   paramiko.transport: Compression: none
DEB [20250714-13:21:11.283] thr=6   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:21:11.384] thr=6   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:21:11.387] thr=6   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:21:11.389] thr=6   paramiko.transport: Switch to new keys ...
DEB [20250714-13:21:11.390] thr=6   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:21:11.391] thr=2   paramiko.transport: Attempting password auth...
DEB [20250714-13:21:11.393] thr=6   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:21:11.536] thr=6   paramiko.transport: userauth is OK
INF [20250714-13:21:13.500] thr=6   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:21:13.504] thr=6   paramiko.transport: Authentication (password) failed.
DEB [20250714-13:21:49.746] thr=1   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:21:49.748] thr=1   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:22:11.240] thr=6   paramiko.transport: EOF in transport thread
DEB [20250714-13:22:17.066] thr=7   paramiko.transport: starting thread (client mode): 0xc7a72ba0
DEB [20250714-13:22:17.070] thr=7   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:22:17.204] thr=7   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:22:17.206] thr=7   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:22:17.237] thr=7   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:22:17.238] thr=7   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:22:17.239] thr=7   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:22:17.239] thr=7   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:22:17.240] thr=7   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:22:17.242] thr=7   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:22:17.242] thr=7   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:22:17.243] thr=7   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:22:17.244] thr=7   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:22:17.244] thr=7   paramiko.transport: client lang: <none>
DEB [20250714-13:22:17.244] thr=7   paramiko.transport: server lang: <none>
DEB [20250714-13:22:17.246] thr=7   paramiko.transport: kex follows: False
DEB [20250714-13:22:17.246] thr=7   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:22:17.247] thr=7   paramiko.transport: Strict kex mode: True
DEB [20250714-13:22:17.248] thr=7   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:22:17.249] thr=7   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:22:17.249] thr=7   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:22:17.250] thr=7   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:22:17.250] thr=7   paramiko.transport: Compression: none
DEB [20250714-13:22:17.251] thr=7   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:22:17.352] thr=7   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:22:17.353] thr=7   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:22:17.354] thr=7   paramiko.transport: Switch to new keys ...
DEB [20250714-13:22:17.354] thr=7   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:22:17.357] thr=2   paramiko.transport: Adding ssh-ed25519 host key for tpc84599bg1112.tgrc.tesco.org: b'6cfba7a142f6a8ca4586a24ffd280ce7'
DEB [20250714-13:22:17.357] thr=7   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:22:17.493] thr=7   paramiko.transport: userauth is OK
INF [20250714-13:22:19.458] thr=7   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:22:19.460] thr=7   paramiko.transport: Authentication (password) failed.
DEB [20250714-13:23:17.206] thr=7   paramiko.transport: EOF in transport thread
DEB [20250714-13:23:48.469] thr=8   paramiko.transport: starting thread (client mode): 0xcae79b80
DEB [20250714-13:23:48.484] thr=8   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:23:48.603] thr=8   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:23:48.604] thr=8   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:23:48.634] thr=8   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:23:48.634] thr=8   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:23:48.635] thr=8   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:23:48.635] thr=8   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:23:48.636] thr=8   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:23:48.637] thr=8   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:23:48.637] thr=8   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:23:48.638] thr=8   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:23:48.639] thr=8   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:23:48.639] thr=8   paramiko.transport: client lang: <none>
DEB [20250714-13:23:48.639] thr=8   paramiko.transport: server lang: <none>
DEB [20250714-13:23:48.640] thr=8   paramiko.transport: kex follows: False
DEB [20250714-13:23:48.640] thr=8   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:23:48.642] thr=8   paramiko.transport: Strict kex mode: True
DEB [20250714-13:23:48.642] thr=8   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:23:48.643] thr=8   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:23:48.644] thr=8   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:23:48.644] thr=8   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:23:48.645] thr=8   paramiko.transport: Compression: none
DEB [20250714-13:23:48.646] thr=8   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:23:48.748] thr=8   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:23:48.749] thr=8   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:23:48.749] thr=8   paramiko.transport: Switch to new keys ...
DEB [20250714-13:23:48.751] thr=8   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:23:48.752] thr=2   paramiko.transport: Adding ssh-ed25519 host key for tpc84599bg1112.tgrc.tesco.org: b'6cfba7a142f6a8ca4586a24ffd280ce7'
DEB [20250714-13:23:48.752] thr=8   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:23:48.893] thr=8   paramiko.transport: userauth is OK
INF [20250714-13:23:52.722] thr=8   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:23:52.723] thr=8   paramiko.transport: Authentication (password) failed.
DEB [20250714-13:24:15.575] thr=4   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:24:15.577] thr=4   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:24:48.629] thr=8   paramiko.transport: EOF in transport thread
DEB [20250714-13:26:49.965] thr=1   paramiko.transport: Received global request "<EMAIL>"
DEB [20250714-13:26:49.966] thr=1   paramiko.transport: Rejecting "<EMAIL>" global request from server.
DEB [20250714-13:28:22.653] thr=9   paramiko.transport: starting thread (client mode): 0xc7a8b560
DEB [20250714-13:28:22.656] thr=9   paramiko.transport: Local version/idstring: SSH-2.0-paramiko_3.4.1
DEB [20250714-13:28:22.809] thr=9   paramiko.transport: Remote version/idstring: SSH-2.0-OpenSSH_8.7
INF [20250714-13:28:22.810] thr=9   paramiko.transport: Connected (version 2.0, client OpenSSH_8.7)
DEB [20250714-13:28:22.832] thr=9   paramiko.transport: === Key exchange possibilities ===
DEB [20250714-13:28:22.833] thr=9   paramiko.transport: kex algos: curve25519-sha256, <EMAIL>, ecdh-sha2-nistp256, ecdh-sha2-nistp384, ecdh-sha2-nistp521, diffie-hellman-group-exchange-sha256, diffie-hellman-group14-sha256, diffie-hellman-group16-sha512, diffie-hellman-group18-sha512, <EMAIL>
DEB [20250714-13:28:22.834] thr=9   paramiko.transport: server key: rsa-sha2-512, rsa-sha2-256, ecdsa-sha2-nistp256, ssh-ed25519, <EMAIL>
DEB [20250714-13:28:22.836] thr=9   paramiko.transport: client encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:28:22.836] thr=9   paramiko.transport: server encrypt: <EMAIL>, <EMAIL>, aes256-ctr, <EMAIL>, aes128-ctr
DEB [20250714-13:28:22.837] thr=9   paramiko.transport: client mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:28:22.838] thr=9   paramiko.transport: server mac: <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, hmac-sha2-256, hmac-sha1, <EMAIL>, hmac-sha2-512
DEB [20250714-13:28:22.839] thr=9   paramiko.transport: client compress: none, <EMAIL>
DEB [20250714-13:28:22.839] thr=9   paramiko.transport: server compress: none, <EMAIL>
DEB [20250714-13:28:22.840] thr=9   paramiko.transport: client lang: <none>
DEB [20250714-13:28:22.841] thr=9   paramiko.transport: server lang: <none>
DEB [20250714-13:28:22.841] thr=9   paramiko.transport: kex follows: False
DEB [20250714-13:28:22.842] thr=9   paramiko.transport: === Key exchange agreements ===
DEB [20250714-13:28:22.843] thr=9   paramiko.transport: Strict kex mode: True
DEB [20250714-13:28:22.843] thr=9   paramiko.transport: Kex: <EMAIL>
DEB [20250714-13:28:22.844] thr=9   paramiko.transport: HostKey: ssh-ed25519
DEB [20250714-13:28:22.845] thr=9   paramiko.transport: Cipher: aes128-ctr
DEB [20250714-13:28:22.845] thr=9   paramiko.transport: MAC: hmac-sha2-256
DEB [20250714-13:28:22.846] thr=9   paramiko.transport: Compression: none
DEB [20250714-13:28:22.847] thr=9   paramiko.transport: === End of kex handshake ===
DEB [20250714-13:28:22.950] thr=9   paramiko.transport: Resetting outbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:28:22.950] thr=9   paramiko.transport: kex engine KexCurve25519 specified hash_algo <built-in function openssl_sha256>
DEB [20250714-13:28:22.952] thr=9   paramiko.transport: Switch to new keys ...
DEB [20250714-13:28:22.953] thr=9   paramiko.transport: Resetting inbound seqno after NEWKEYS due to strict mode
DEB [20250714-13:28:22.955] thr=2   paramiko.transport: Attempting password auth...
DEB [20250714-13:28:22.955] thr=9   paramiko.transport: Got EXT_INFO: {'server-sig-algs': b'ssh-ed25519,<EMAIL>,ssh-rsa,rsa-sha2-256,rsa-sha2-512,ssh-dss,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,<EMAIL>,<EMAIL>'}
DEB [20250714-13:28:23.089] thr=9   paramiko.transport: userauth is OK
INF [20250714-13:28:26.917] thr=9   paramiko.transport: Auth banner: b'****************************************************************************************************\n*                        This system is the property of Tesco Stores Plc.                          *\n* Before proceeding be aware that...                                                               *\n****************************************************************************************************\n* This is a private computer system with access restricted to those with authorisation. If you are *\n* not specifically authorised to access data on this system, disconnect now. All information and   *\n* communications on this system are subject to review, monitoring and recording at any time        *\n* without notice and permission.                                                                   *\n* Unauthorised use or access may be subject to prosecution or disciplinary action.                 *\n****************************************************************************************************\n'
INF [20250714-13:28:26.919] thr=9   paramiko.transport: Authentication (password) failed.
DEB [20250714-13:28:43.300] thr=4   paramiko.transport: EOF in transport thread
DEB [20250714-13:28:43.301] thr=1   paramiko.transport: EOF in transport thread
DEB [20250714-13:28:43.301] thr=9   paramiko.transport: EOF in transport thread
