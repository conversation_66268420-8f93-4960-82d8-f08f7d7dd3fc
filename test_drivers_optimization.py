"""
Performance testing script for Repl_Drivers_Calculation_TPN optimization.

This script compares the performance of the original pandas-based implementation
with the optimized Polars-based implementation.
"""

import pandas as pd
import numpy as np
import time
import psutil
import os
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

# Import the functions to test
import Replenishment_Model_Functions_25 as rmf

def get_memory_usage():
    """Get current memory usage in MB"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def create_test_dataset(num_stores=100, num_tpns_per_store=1000):
    """Create a synthetic test dataset similar to the real Repl_Dataset"""
    
    print(f"🔧 Creating test dataset: {num_stores} stores × {num_tpns_per_store} TPNs = {num_stores * num_tpns_per_store:,} rows")
    
    # Generate store list
    stores = [f"STORE_{i:04d}" for i in range(1, num_stores + 1)]
    
    # Generate base data
    np.random.seed(42)  # For reproducible results
    
    data = []
    for store in stores:
        for tpn_idx in range(num_tpns_per_store):
            row = {
                'country': np.random.choice(['SK', 'CZ', 'HU']),
                'store': store,
                'day': np.random.choice(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']),
                'dep': np.random.choice(['DRY', 'BWS', 'HEA', 'PRO', 'GM', 'HDL']),
                'pmg': f"PMG{np.random.randint(1, 50):02d}",
                'tpnb': f"TPN{tpn_idx:06d}",
                'format': np.random.choice(['Express', 'Metro', 'Extra', '1K']),
                'division': np.random.choice(['Grocery', 'GM']),
                'stock': np.random.randint(0, 100),
                'sold_units': np.random.randint(0, 50),
                'sales_excl_vat': np.random.uniform(0, 100),
                'cases_delivered': np.random.randint(0, 20),
                'case_capacity': np.random.randint(6, 24),
                'pallet_capacity': np.random.randint(40, 120),
                'shelfCapacity': np.random.randint(10, 50),
                'weight': np.random.uniform(0.1, 5.0),
                'unit': np.random.randint(1, 100),
                'foil': np.random.choice([0, 1], p=[0.3, 0.7]),
                'srp': np.random.choice([0, 1], p=[0.7, 0.3]),
                'nsrp': np.random.choice([0, 1], p=[0.6, 0.4]),
                'full_pallet': np.random.choice([0, 1], p=[0.9, 0.1]),
                'mu': np.random.choice([0, 1], p=[0.95, 0.05]),
                'split_pallet': np.random.choice([0, 1], p=[0.8, 0.2]),
                'icream_nsrp': np.random.choice([0, 1], p=[0.95, 0.05]),
                'single_pick': np.random.choice([0, 1], p=[0.9, 0.1]),
                'broken_case_flag': np.random.choice([0, 1], p=[0.9, 0.1]),
                'backroom_flag': np.random.choice([0, 1], p=[0.8, 0.2]),
                'clipstrip_flag': np.random.choice([0, 1], p=[0.9, 0.1]),
                'is_capping_shelf': np.random.choice([0, 1], p=[0.7, 0.3]),
                'sold_units_dotcom': np.random.randint(0, 10),
                'ownbrand': np.random.choice([0, 1], p=[0.6, 0.4]),
                'checkout_stand_flag': np.random.choice([0, 1], p=[0.95, 0.05]),
                'product_name': f"Product_{tpn_idx}",
                'opening_type': np.random.choice(['Tray + Hood', 'Perforated box', 'Shrink', 'Tray + Shrink', 'Tray', 'no_data'], 
                                               p=[0.2, 0.2, 0.2, 0.2, 0.1, 0.1]),
                'unit_type': np.random.choice(['EA', 'KG'], p=[0.8, 0.2]),
                'shelfservice_flag': np.random.choice([0, 1], p=[0.9, 0.1]),
                'SRP opening reduction opportunity': np.random.choice([0, 1], p=[0.8, 0.2]),
                'extra disassemble %': np.random.uniform(0, 0.5),
                # Add missing columns that might be required
                'pre_sort_perc_by_pmg': np.random.uniform(0.1, 0.3),
                'MIX_CAGE_%': np.random.uniform(0.3, 0.7),
                'PBL_CAGE_%': np.random.uniform(0.2, 0.5),
                'PBS_CAGE_%': np.random.uniform(0.2, 0.5),
                'MIX_PALLET_%': np.random.uniform(0.3, 0.7),
                'PBL_PALLET_%': np.random.uniform(0.2, 0.5),
                'PBS_PALLET_%': np.random.uniform(0.2, 0.5)
            }
            data.append(row)

    df = pd.DataFrame(data)
    print(f"✅ Test dataset created: {len(df):,} rows, {df.memory_usage(deep=True).sum() / 1024 / 1024:.1f} MB")
    return df

def create_test_store_inputs(stores):
    """Create synthetic store_inputs DataFrame"""
    
    data = []
    for store in stores:
        for dep in ['DRY', 'BWS', 'HEA', 'PRO', 'GM', 'HDL']:
            row = {
                'Store': store,
                'Format': np.random.choice(['Express', 'Metro', 'Extra', '1K']),
                'Plan Size': np.random.randint(1000, 5000),
                'Dep': dep,
                'Racking': np.random.choice([0, 1], p=[0.7, 0.3]),
                'Pallets Delivery Ratio': np.random.uniform(0.3, 0.8),
                'Backstock Pallet Ratio': np.random.uniform(0.2, 0.6),
                'says': np.random.uniform(0.1, 0.3),
                'pbl_pbs_25_perc_pallet': np.random.uniform(0.2, 0.3),
                'Country': np.random.choice(['SK', 'CZ', 'HU']),
                'Pmg': f"PMG{np.random.randint(1, 50):02d}",
                'presortPerc': np.random.uniform(0.1, 0.4),
                'prack': np.random.uniform(0.1, 0.3)
            }
            data.append(row)
    
    return pd.DataFrame(data)

def run_performance_test(test_size="small"):
    """Run performance comparison test"""
    
    print(f"\n{'='*60}")
    print(f"🚀 PERFORMANCE TEST: {test_size.upper()} DATASET")
    print(f"{'='*60}")
    
    # Define test sizes
    test_configs = {
        "small": {"stores": 10, "tpns": 100},
        "medium": {"stores": 50, "tpns": 500}, 
        "large": {"stores": 100, "tpns": 1000},
        "xlarge": {"stores": 200, "tpns": 1500}
    }
    
    config = test_configs[test_size]
    
    # Create test data
    print(f"\n📊 Creating test dataset...")
    initial_memory = get_memory_usage()
    
    Repl_Dataset = create_test_dataset(config["stores"], config["tpns"])
    stores = Repl_Dataset['store'].unique().tolist()
    store_inputs = create_test_store_inputs(stores)
    
    dataset_memory = get_memory_usage()
    print(f"💾 Memory usage after dataset creation: {dataset_memory - initial_memory:.1f} MB")
    
    # Create a dummy Excel file for testing if it doesn't exist
    dummy_excel_path = Path('.') / 'dummy_test.xlsx'
    if not dummy_excel_path.exists():
        try:
            # Create minimal Excel file with required sheets
            with pd.ExcelWriter(dummy_excel_path, engine='openpyxl') as writer:
                # Create dummy produce_dataframe sheet
                produce_df = pd.DataFrame({
                    'pmg': [f'PRO{i:02d}' for i in range(1, 20)],
                    'replenishment_type': ['Multideck'] * 19,
                    'rc_capacity': [10] * 19,
                    'crate_size': ['Large'] * 19
                })
                produce_df.to_excel(writer, sheet_name='produce_dataframe', index=False)

                # Create dummy produce_modules sheet
                modules_df = pd.DataFrame({
                    'store': stores[:min(10, len(stores))],
                    'tables': [5] * min(10, len(stores)),
                    'multidecks': [8] * min(10, len(stores))
                })
                modules_df.to_excel(writer, sheet_name='produce_modules', index=False)

            print(f"📝 Created dummy Excel file: {dummy_excel_path}")
        except Exception as e:
            print(f"⚠️  Could not create dummy Excel file: {e}")

    # Test parameters (simplified for testing)
    test_params = {
        'directory': Path('.'),
        'backstock_target': 0.2,
        'RC_Capacity_Ratio': 1.3,
        'shelf_trolley_cap_ratio_to_pallet': 0.8,
        'shelf_trolley_cap_ratio_to_rollcage': 0.6,
        'excel_inputs_f': 'dummy_test.xlsx',
        'MODULE_CRATES': 10,
        'TABLE_CRATES': 15,
        'FULFILL_TARGET': 0.8,
        'SALES_CYCLE': [1, 2, 3, 4, 5],
        'RC_CAPACITY': 1.2,
        'RC_DELIVERY': 0.7,
        'RC_VS_PAL_CAPACITY': 0.9,
        'only_tpn': False,
        'tpnb_store': False,
        'tpnb_country': False,
        'selected_tpn': None,
        'capping_shelves_ratio': 1.5,
        'version': 'test',
        'shelfService_gm': False,
        'cases_to_replenish_only': False
    }
    
    print(f"\n🔬 Running performance comparison...")
    
    # Test 1: Optimized version
    print(f"\n1️⃣  Testing OPTIMIZED version...")
    start_memory = get_memory_usage()
    start_time = time.time()
    
    try:
        drivers_opt, drivers_produce_opt = rmf.Repl_Drivers_Calculation_TPN_optimized(
            Repl_Dataset=Repl_Dataset,
            store_inputs=store_inputs,
            stores=stores,
            **test_params
        )
        
        opt_time = time.time() - start_time
        opt_memory = get_memory_usage() - start_memory
        opt_success = True
        
        print(f"✅ Optimized version completed!")
        print(f"⏱️  Time: {opt_time:.2f} seconds")
        print(f"💾 Memory: {opt_memory:.1f} MB")
        print(f"📊 Output: {len(drivers_opt):,} drivers, {len(drivers_produce_opt):,} produce")
        
    except Exception as e:
        print(f"❌ Optimized version failed: {str(e)}")
        opt_time = float('inf')
        opt_memory = float('inf')
        opt_success = False
    
    # Test 2: Original version (only for smaller datasets due to time constraints)
    if test_size in ["small", "medium"]:
        print(f"\n2️⃣  Testing ORIGINAL version...")
        start_memory = get_memory_usage()
        start_time = time.time()
        
        try:
            drivers_orig, drivers_produce_orig = rmf.Repl_Drivers_Calculation_TPN_original(
                Repl_Dataset=Repl_Dataset,
                store_inputs=store_inputs,
                stores=stores,
                **test_params
            )
            
            orig_time = time.time() - start_time
            orig_memory = get_memory_usage() - start_memory
            orig_success = True
            
            print(f"✅ Original version completed!")
            print(f"⏱️  Time: {orig_time:.2f} seconds")
            print(f"💾 Memory: {orig_memory:.1f} MB")
            print(f"📊 Output: {len(drivers_orig):,} drivers, {len(drivers_produce_orig):,} produce")
            
        except Exception as e:
            print(f"❌ Original version failed: {str(e)}")
            orig_time = float('inf')
            orig_memory = float('inf')
            orig_success = False
    else:
        print(f"\n2️⃣  Skipping original version for {test_size} dataset (too slow)")
        orig_time = None
        orig_memory = None
        orig_success = None
    
    # Results summary
    print(f"\n{'='*60}")
    print(f"📈 PERFORMANCE RESULTS - {test_size.upper()} DATASET")
    print(f"{'='*60}")
    print(f"Dataset size: {len(Repl_Dataset):,} rows, {len(stores)} stores")
    
    if opt_success:
        print(f"✅ Optimized: {opt_time:.2f}s, {opt_memory:.1f} MB")
    else:
        print(f"❌ Optimized: FAILED")
    
    if orig_success is not None:
        if orig_success:
            print(f"🐌 Original:  {orig_time:.2f}s, {orig_memory:.1f} MB")
            
            if opt_success and orig_success:
                speedup = orig_time / opt_time
                memory_improvement = (orig_memory - opt_memory) / orig_memory * 100
                print(f"\n🚀 IMPROVEMENT:")
                print(f"   ⚡ Speed: {speedup:.1f}x faster")
                print(f"   💾 Memory: {memory_improvement:.1f}% reduction")
        else:
            print(f"❌ Original:  FAILED")
    else:
        print(f"⏭️  Original:  SKIPPED")
    
    return {
        'test_size': test_size,
        'dataset_rows': len(Repl_Dataset),
        'num_stores': len(stores),
        'opt_time': opt_time if opt_success else None,
        'opt_memory': opt_memory if opt_success else None,
        'orig_time': orig_time if orig_success else None,
        'orig_memory': orig_memory if orig_success else None,
        'speedup': orig_time / opt_time if (opt_success and orig_success) else None
    }

if __name__ == "__main__":
    print("🧪 Drivers Calculation Performance Testing")
    print("=" * 50)
    
    # Run tests for different dataset sizes
    results = []
    
    for test_size in ["small", "medium"]:  # Start with smaller tests
        try:
            result = run_performance_test(test_size)
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test_size} failed: {str(e)}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🏁 FINAL SUMMARY")
    print(f"{'='*60}")
    
    for result in results:
        if result['speedup']:
            print(f"{result['test_size'].upper()}: {result['speedup']:.1f}x speedup ({result['dataset_rows']:,} rows)")
        else:
            print(f"{result['test_size'].upper()}: Performance test incomplete")
